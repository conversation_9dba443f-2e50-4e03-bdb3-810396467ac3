import{a as q,b as W,c as z,d as G,e as J}from"./chunk-XPDK2PIO.js";import{a as L,b as j}from"./chunk-4UYMFPPF.js";import"./chunk-OLNEY35K.js";import{a as $}from"./chunk-JWVI4R37.js";import{a as D}from"./chunk-QJAYFQIR.js";import{a as H}from"./chunk-Y2PVWY7X.js";import"./chunk-MMC3E6BY.js";import"./chunk-QAUHHFOT.js";import"./chunk-VBHJ4LBO.js";import{c as V,d as B}from"./chunk-FUCZYBK4.js";import"./chunk-OTT6DUE3.js";import"./chunk-22JWGO27.js";import"./chunk-SXMRENJM.js";import"./chunk-BMA7WWEI.js";import{D as P,d as M,e as R,k as O}from"./chunk-GDGXRFMB.js";import{$b as I,Ab as n,Bb as A,Fb as g,Kb as d,Lb as _,Oa as v,Pb as w,Ta as l,Vb as p,Wb as E,Xb as k,Z as s,Zb as N,_b as F,a as u,cb as h,fa as f,ga as C,ib as T,qb as a,sa as y,wb as b,xb as S,yb as x,zb as c}from"./chunk-YUW2MUHJ.js";import"./chunk-EQDQRRRY.js";function Q(o,e){if(o&1){let t=g();c(0,"p-accordion-panel",4)(1,"p-accordion-header")(2,"p",10)(3,"a",11),p(4),n(),c(5,"p-button",12),d("click",function(){let m=f(t).$implicit,r=_();return C(r.onPlayClick(m))}),n()()(),c(6,"p-accordion-content"),A(7,"p",13),n()()}if(o&2){let t=e.$implicit;a("value",t.id),l(3),w("name","_Toc",t.id,""),l(),E(t.title),l(3),a("innerHTML",t.content,v)}}function U(o,e){o&1&&(c(0,"div",14),p(1," \u6682\u65E0\u76EE\u5F55 "),n())}function X(o,e){if(o&1){let t=g();c(0,"a",15),d("click",function(){let m=f(t).$implicit,r=_();return C(r.onTocItemClick(m))}),p(1),n()}if(o&2){let t=e.$implicit;l(),k(" ",t.title," ")}}var K=class o{constructor(){this.#e=s(P);this.#t=s(D);this.#i=s($);this.elementRef=s(y);this.i18nService=s(H);this.subs=new u;this.contentCode=null;this.files=[];this.items=[]}#e;#t;#i;ngOnInit(){this.#e.queryParams.subscribe(e=>{if(!e.contentCode)return;this.contentCode=e.contentCode,this.subs.unsubscribe(),this.subs=new u;let t=this.i18nService.language$.subscribe(()=>{this.loadCollectionSummary()});this.subs.add(t)})}loadCollectionSummary(){this.#t.getCollectionTreeByContentCode(this.contentCode).subscribe({next:e=>{this.files=e.map(t=>this.buildTreeNode(t))},error:e=>{console.error("\u83B7\u53D6\u6587\u7AE0\u6811\u6570\u636E\u5931\u8D25:",e)}})}buildTreeNode(e){return{key:e.contentCode,label:e.name||e.title,data:e,children:e.children?e.children.map(t=>this.buildTreeNode(t)):[]}}loadArticleSummary(e){e.key&&this.#i.getArticleAggregatesByCollectionContentCode(e.key).subscribe({next:t=>{this.items=t},error:t=>{console.error("\u83B7\u53D6\u6587\u7AE0\u8BE6\u60C5\u5931\u8D25:",t)}})}onTocItemClick(e){this.scrollToAnchor("_Toc"+e.id)}scrollToAnchor(e){let t=this.elementRef.nativeElement.querySelector(".articaldetail-container");if(!t)return;let i=t.querySelector(`a[name="${e}"]`);i?i.scrollIntoView({behavior:"smooth",block:"start",inline:"nearest"}):console.warn(`\u672A\u627E\u5230\u951A\u70B9: ${e}`)}onPlayClick(e){console.log("Play clicked for item:",e)}ngOnDestroy(){this.subs.unsubscribe()}static{this.\u0275fac=function(t){return new(t||o)}}static{this.\u0275cmp=h({type:o,selectors:[["app-collection-tree"]],decls:13,vars:6,consts:[[1,"flex","flex-1"],["styleClass","w-[20rem] h-full","selectionMode","single","virtualScrollItemSize","36",3,"selectionChange","onNodeSelect","value","selection","virtualScroll","filter"],[1,"articaldetail-container","prose","max-w-none","p-6","flex-1","overflow-y-auto",2,"height","calc(100vh - 5rem)"],["value","0"],[3,"value"],[1,"p-6","w-[20rem]","border-l-2"],[1,"flex","flex-col","max-h-[80vh]","overflow-y-auto"],[1,"text-lg","font-semibold","mb-4"],["class","text-sm italic",4,"ngIf"],["class","mt-2 cursor-pointer hover:text-primary-600 underline",3,"click",4,"ngFor","ngForOf"],[1,"flex-1","flex","justify-between","items-center","px-5"],[3,"name"],["icon","pi pi-play-circle","label","\u64AD\u653E","variant","outlined",3,"click"],[1,"m-0",3,"innerHTML"],[1,"text-sm","italic"],[1,"mt-2","cursor-pointer","hover:text-primary-600","underline",3,"click"]],template:function(t,i){t&1&&(c(0,"div",0)(1,"p-tree",1),I("selectionChange",function(r){return F(i.selectedFile,r)||(i.selectedFile=r),r}),d("onNodeSelect",function(r){return i.loadArticleSummary(r.node)}),n(),c(2,"div",2)(3,"p-accordion",3),S(4,Q,8,5,"p-accordion-panel",4,b),n()(),c(6,"div",5)(7,"div")(8,"div",6)(9,"h3",7),p(10,"\u76EE\u5F55"),n(),T(11,U,2,0,"div",8)(12,X,2,1,"a",9),n()()()()),t&2&&(l(),a("value",i.files),N("selection",i.selectedFile),a("virtualScroll",!0)("filter",!0),l(3),x(i.items),l(7),a("ngIf",i.items.length===0),l(),a("ngForOf",i.items))},dependencies:[O,M,R,j,L,J,G,q,W,z,B,V],styles:["[_nghost-%COMP%]{flex:1;display:flex}[_nghost-%COMP%]     .p-virtualscroller{height:calc(100% - 30px)!important}"]})}};export{K as CollectionTreeComponent};
