# ASP.NET Core
# Build and test ASP.NET Core projects targeting .NET Core.
# Add steps that run tests, create a NuGet package, deploy, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/dotnet-core

trigger: none
#  branches:
#    include:
#      - main
#  paths:
#    include:
#      - 'Holybless.sln'
#      - 'src/**'
#      - 'tests/**'
#      - 'azure-pipelines.yml'

pool:
  name: Default

variables:
  buildConfiguration: 'Release'
  publishDir: '$(Build.ArtifactStagingDirectory)/publish'

steps:
# Installed .Net SDK 9.0.x on the VM manually. Skip this step
- task: UseDotNet@2
  inputs:
    packageType: 'sdk'
    version: '9.0.x'
    includePreviewVersions: false

- script: dotnet --version
  displayName: 'Show installed .NET SDK version'

# Optional: Add global .NET tools directory to PATH
#- script: |
#   echo "$(HOME)/.dotnet/tools" >> $GITHUB_PATH
#  displayName: 'Update PATH for dotnet tools'
  
- script: dotnet restore HolyBless.sln
  displayName: 'Restore NuGet packages'

- powershell: |
    $appSettingsPath = "src/HolyBless.HttpApi.Host/appsettings.json"
    $devSettingsPath = "src/HolyBless.HttpApi.Host/appsettings.QA.json"
    $devContent = Get-Content $devSettingsPath -Raw | ConvertFrom-Json
    $appContent = Get-Content $appSettingsPath -Raw | ConvertFrom-Json
    $devContent | ConvertTo-Json -Depth 10 | Set-Content $appSettingsPath
  displayName: 'Update BE appsettings.json with DEV settings'

- powershell: |
    $appSettingsPath = "src/HolyBless.DbMigrator/appsettings.json"
    $devSettingsPath = "src/HolyBless.DbMigrator/appsettings.QA.json"
    $devContent = Get-Content $devSettingsPath -Raw | ConvertFrom-Json
    $appContent = Get-Content $appSettingsPath -Raw | ConvertFrom-Json
    $devContent | ConvertTo-Json -Depth 10 | Set-Content $appSettingsPath
  displayName: 'Update DbMigrator appsettings.json with DEV settings'

# Manually installed ABP CLI on the VM. Skip this step
#- script: |
#    dotnet tool install -g Volo.Abp.Studio.Cli
#    abp --version
#  displayName: 'Install ABP CLI'

#- script: |
#    cd src/HolyBless.HttpApi.Host.HttpApi.Host
#    abp install-libs
#  displayName: 'Install ABP Frontend Libraries'

- script: dotnet build HolyBless.sln --configuration Release
  displayName: 'Build the solution'

- powershell: |
    $dbMigratorDir = "src/HolyBless.DbMigrator/bin/Release/net9.0"
    Write-Host "Changing to directory: $dbMigratorDir"
    Push-Location $dbMigratorDir
    try {
        Write-Host "Running DbMigrator..."
        .\HolyBless.DbMigrator.exe
        if ($LASTEXITCODE -ne 0) {
            Write-Error "Database migration failed with exit code $LASTEXITCODE"
            exit $LASTEXITCODE
        }
    }
    finally {
        Pop-Location
    }
  displayName: 'Run Database Migrations'

- task: DotNetCoreCLI@2
  inputs:
    command: 'publish'
    publishWebProjects: true
    arguments: '--configuration $(buildConfiguration) --output $(publishDir)'
    zipAfterPublish: false

- powershell: |
    Remove-Item -Recurse -Force "C:\inetpub\wwwroot\HolyblessUpgrade\*" -ErrorAction SilentlyContinue
  displayName: 'Clean existing wwwroot/HolyblessUpgrade'

# Copy published app to IIS wwwroot
- powershell: |
    Copy-Item -Path "$(publishDir)\HolyBless.HttpApi.Host\*" -Destination "C:\inetpub\wwwroot\HolyblessUpgrade" -Recurse -Force
  displayName: 'Deploy to wwwroot'

- powershell: |
    $certSource = "C:\inetpub\openiddict.pfx"
    $certDestination = "C:\inetpub\wwwroot\HolyblessUpgrade\openiddict.pfx"
    Write-Host "Copying certificate from $certSource to $certDestination"
    Copy-Item -Path $certSource -Destination $certDestination -Force
    if (-not (Test-Path $certDestination)) {
        Write-Error "Failed to copy certificate file"
        exit 1
    }
  displayName: 'Copy OpenIddict Certificate'