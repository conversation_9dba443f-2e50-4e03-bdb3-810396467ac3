import{c as l}from"./chunk-MMC3E6BY.js";import{T as a,Y as i}from"./chunk-YUW2MUHJ.js";import{a as r}from"./chunk-EQDQRRRY.js";var s=class o{constructor(e){this.restService=e;this.apiName="Default";this.getFolderFiles=(e,t)=>this.restService.request({method:"GET",url:"/api/app/read-only-virtual-folder/folder-files",params:{folderId:e.folderId,contentCode:e.contentCode,sorting:e.sorting,skipCount:e.skipCount,maxResultCount:e.maxResultCount}},r({apiName:this.apiName},t));this.getList=(e,t)=>this.restService.request({method:"GET",url:"/api/app/read-only-virtual-folder",params:{channelId:e.channelId,contentCode:e.contentCode,sorting:e.sorting,skipCount:e.skipCount,maxResultCount:e.maxResultCount}},r({apiName:this.apiName},t));this.getVirtualFolderTree=(e,t)=>this.restService.request({method:"GET",url:`/api/app/read-only-virtual-folder/virtual-folder-tree/${e}`},r({apiName:this.apiName},t));this.getVirtualFolderTreeByContentCode=(e,t)=>this.restService.request({method:"GET",url:"/api/app/read-only-virtual-folder/virtual-folder-tree-by-content-code",params:{contentCode:e}},r({apiName:this.apiName},t))}static{this.\u0275fac=function(t){return new(t||o)(i(l))}}static{this.\u0275prov=a({token:o,factory:o.\u0275fac,providedIn:"root"})}};export{s as a};
