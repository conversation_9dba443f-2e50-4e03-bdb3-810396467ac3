using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using HolyBless.Localization;
using HolyBless.MultiTenancy;
using Volo.Abp.Localization;
using Volo.Abp.Modularity;
using Volo.Abp.MultiTenancy;
using Volo.Abp.PermissionManagement.Identity;
using Volo.Abp.SettingManagement;
using Volo.Abp.BlobStoring.Database;
using Volo.Abp.Caching;
using Volo.Abp.OpenIddict;
using Volo.Abp.PermissionManagement.OpenIddict;
using Volo.Abp.AuditLogging;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.Emailing;
using Volo.Abp.FeatureManagement;
using Volo.Abp.Identity;
using System;
using Volo.Abp.Data;
using HolyBless.DataSeeders;
using System.Linq;

namespace HolyBless;

[DependsOn(
    typeof(HolyBlessDomainSharedModule),
    typeof(AbpAuditLoggingDomainModule),
    typeof(AbpCachingModule),
    typeof(AbpBackgroundJobsDomainModule),
    typeof(AbpFeatureManagementDomainModule),
    typeof(AbpPermissionManagementDomainIdentityModule),
    typeof(AbpPermissionManagementDomainOpenIddictModule),
    typeof(AbpSettingManagementDomainModule),
    typeof(AbpEmailingModule),
    typeof(AbpIdentityDomainModule),
    typeof(AbpOpenIddictDomainModule),
    typeof(BlobStoringDatabaseDomainModule)
    )]
public class HolyBlessDomainModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        Configure<AbpMultiTenancyOptions>(options =>
        {
            options.IsEnabled = MultiTenancyConsts.IsEnabled;
        });

        ConfigureDataSeederOrders();
#if DEBUG
        context.Services.Replace(ServiceDescriptor.Singleton<IEmailSender, NullEmailSender>());
#endif
    }

    public void ConfigureDataSeederOrders()
    {
        Configure<AbpDataSeedOptions>(options =>
        {
            // This is automatic, but we do it here because the order matters.
            var requestedOrder = new Type[] {
                typeof(CountryDataSeederContributor),typeof(ChannelDataSeederContributor),
                typeof(StorageProviderDataSeederContributor),typeof(BookStoreDataSeederContributor),
               };

            var orderedContributors = options.Contributors
                .OrderBy(contributor =>
                {
                    var index = Array.IndexOf(requestedOrder, contributor);
                    return index == -1 ? int.MaxValue : index;
                })
                .ToList();

            options.Contributors.Clear();
            foreach (var contributor in orderedContributors)
            {
                options.Contributors.Add(contributor);
            }
        });
    }
}