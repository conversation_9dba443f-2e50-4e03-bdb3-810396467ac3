import{c as m}from"./chunk-MMC3E6BY.js";import{T as r,Y as i}from"./chunk-YUW2MUHJ.js";import{a}from"./chunk-EQDQRRRY.js";var s=class o{constructor(e){this.restService=e;this.apiName="Default";this.getAlbumFiles=(e,t)=>this.restService.request({method:"GET",url:`/api/app/read-only-album/album-files/${e}`},a({apiName:this.apiName},t));this.getAlbumFilesByContentCode=(e,t)=>this.restService.request({method:"GET",url:"/api/app/read-only-album/album-files-by-content-code",params:{contentCode:e}},a({apiName:this.apiName},t));this.getList=(e,t)=>this.restService.request({method:"GET",url:"/api/app/read-only-album",params:{channelId:e.channelId,albumType:e.albumType,channelContentCode:e.channelContentCode,sorting:e.sorting,skipCount:e.skipCount,maxResultCount:e.maxResultCount}},a({apiName:this.apiName},t))}static{this.\u0275fac=function(t){return new(t||o)(i(m))}}static{this.\u0275prov=r({token:o,factory:o.\u0275fac,providedIn:"root"})}};export{s as a};
