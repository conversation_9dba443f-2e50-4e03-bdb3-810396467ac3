import { Component, ElementRef, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TreeNode } from 'primeng/api';
import { TreeModule } from 'primeng/tree';
import { AccordionModule } from 'primeng/accordion';
import { ButtonModule } from 'primeng/button';
import { ActivatedRoute } from '@angular/router';
import { ReadOnlyCollectionService } from '@/proxy/holy-bless/collections';
import { ReadOnlyArticleService } from '@/proxy/holy-bless/articles';
import { ArticleAggregateResult } from '@/proxy/holy-bless/results';
import { I18nService } from '@/services/i18n.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-collection-tree',
  standalone: true,
  imports: [CommonModule, TreeModule, AccordionModule, ButtonModule],
  templateUrl: './collection-tree.html',
  styleUrls: ['./collection-tree.scss'],
})
export class CollectionTreeComponent {
  #route = inject(ActivatedRoute);
  #ReadOnlyCollectionService = inject(ReadOnlyCollectionService);
  #ReadOnlyArticleService = inject(ReadOnlyArticleService);
  elementRef = inject(ElementRef);
  i18nService = inject(I18nService);
  subs = new Subscription();

  contentCode: string | null = null;
  files: TreeNode[] = [];
  selectedFile!: TreeNode;
  items: ArticleAggregateResult[] = [];
  ngOnInit() {
    this.#route.queryParams.subscribe((params) => {
      if (!params['contentCode']) return;
      this.contentCode = params['contentCode'];
      this.subs.unsubscribe();
      this.subs = new Subscription();
      const sub = this.i18nService.language$.subscribe(() => {
        this.loadCollectionSummary();
      });
      this.subs.add(sub);
    });
  }

  loadCollectionSummary() {
    this.#ReadOnlyCollectionService
      .getCollectionTreeByContentCode(this.contentCode!)
      .subscribe({
        next: (data) => {
          this.files = data.map((node: any) => this.buildTreeNode(node));
        },
        error: (error) => {
          console.error('获取文章树数据失败:', error);
        },
      });
  }

  buildTreeNode(node: any): TreeNode {
    return {
      key: node.contentCode,
      label: node.name || node.title,
      data: node,
      children: node.children
        ? node.children.map((article: any) => this.buildTreeNode(article))
        : [],
    };
  }

  loadArticleSummary(node: TreeNode) {
    if (!node.key) return;
    this.#ReadOnlyArticleService
      .getArticleAggregatesByCollectionContentCode(node.key)
      .subscribe({
        next: (data) => {
          this.items = data;
        },
        error: (error) => {
          console.error('获取文章详情失败:', error);
        },
      });
  }

  onTocItemClick(item: ArticleAggregateResult) {
    this.scrollToAnchor('_Toc' + item.id);
  }

  private scrollToAnchor(anchorId: string) {
    const container = this.elementRef.nativeElement.querySelector(
      '.articaldetail-container',
    );
    if (!container) return;

    let targetElement = container.querySelector(
      `a[name="${anchorId}"]`,
    ) as HTMLElement;

    if (targetElement) {
      targetElement.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
        inline: 'nearest',
      });
    } else {
      console.warn(`未找到锚点: ${anchorId}`);
    }
  }

  onPlayClick(item: ArticleAggregateResult) {
    console.log('Play clicked for item:', item);
    // 这里可以添加播放逻辑
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
