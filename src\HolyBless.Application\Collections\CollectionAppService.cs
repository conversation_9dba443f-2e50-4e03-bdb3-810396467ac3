﻿using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using HolyBless.Buckets;
using HolyBless.Collections.Dtos;
using HolyBless.Configs;
using HolyBless.Entities.Collections;
using HolyBless.Interfaces;
using HolyBless.Permissions;
using HolyBless.Services;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp.Domain.Repositories;

namespace HolyBless.Collections
{
    [Authorize]
    public class CollectionAppService : ReadOnlyCollectionAppService, ICollectionAppService
    {
        public CollectionAppService(
            ICollectionRepository repository,
            AppConfig settings,
            IRepository<CollectionToArticle> collectionToArticleRepository,
            IRepository<CollectionToFile> collectionToFileRepository,
            IRequestContextService requestContextService,
            ICachedFileUrlAppService cachedFileUrlAppService
            )
            : base(repository, settings, collectionToArticleRepository, collectionToFileRepository, requestContextService, cachedFileUrlAppService)
        {
        }

        [Authorize(HolyBlessPermissions.Collections.Create)]
        public async Task<CollectionDto> CreateAsync(CreateUpdateCollectionDto input)
        {
            var collection = ObjectMapper.Map<CreateUpdateCollectionDto, Collection>(input);
            collection = await _repository.InsertAsync(collection, autoSave: true);
            return ObjectMapper.Map<Collection, CollectionDto>(collection);
        }

        [Authorize(HolyBlessPermissions.Collections.Edit)]
        public async Task<CollectionDto> UpdateAsync(int id, CreateUpdateCollectionDto input)
        {
            var collection = await _repository.GetAsync(id);
            ObjectMapper.Map(input, collection);
            collection = await _repository.UpdateAsync(collection, true);
            return ObjectMapper.Map<Collection, CollectionDto>(collection);
        }

        [Authorize(HolyBlessPermissions.Collections.Edit)]
        public async Task AddArticlesToCollection(int collectionId, List<CollectionToArticleDto> articles)
        {
            var collection = await _repository.GetAsync(collectionId);
            foreach (var articleDto in articles)
            {
                var existing = collection.CollectionToArticles.FirstOrDefault(x => x.ArticleId == articleDto.ArticleId);
                if (existing == null)
                {
                    collection.CollectionToArticles.Add(new Entities.Collections.CollectionToArticle
                    {
                        CollectionId = collectionId,
                        ArticleId = articleDto.ArticleId,
                        Weight = articleDto.Weight
                    });
                }
                else
                {
                    existing.Weight = articleDto.Weight;
                }
            }
            await _repository.UpdateAsync(collection, true);
        }

        [Authorize(HolyBlessPermissions.Collections.Edit)]
        public async Task AddFilesToCollection(int collectionId, List<CollectionToFileDto> files)
        {
            var collection = await _repository.GetAsync(collectionId);
            foreach (var fileDto in files)
            {
                var existing = collection.CollectionToFiles.FirstOrDefault(x => x.FileId == fileDto.FileId);
                if (existing == null)
                {
                    collection.CollectionToFiles.Add(new CollectionToFile
                    {
                        CollectionId = collectionId,
                        FileId = fileDto.FileId,
                        Weight = fileDto.Weight
                    });
                }
                else
                {
                    existing.Weight = fileDto.Weight;
                }
            }
            await _repository.UpdateAsync(collection, true);
        }

        [Authorize(HolyBlessPermissions.Collections.Delete)]
        public async Task DeleteAsync(int id)
        {
            await _repository.DeleteAsync(id, true);
        }

        /// <summary>
        /// Usage Admin page: Link a collection to a channel.
        /// </summary>
        /// <param name="channelId"></param>
        /// <param name="collectionId"></param>
        /// <returns></returns>
        //[Authorize(HolyBlessPermissions.Collections.Default)]
        public async Task LinkToChannel(int channelId, int collectionId)
        {
            var collection = await _repository.GetAsync(collectionId);
            collection.ChannelId = channelId;
            await _repository.UpdateAsync(collection, true);
        }

        /// <summary>
        /// Admin page: Unlink a collection from a channel (Set ChannelId to null)
        /// </summary>
        /// <param name="collectionId"></param>
        /// <returns></returns>
        public async Task UnlinkFromChannel(int collectionId)
        {
            var collection = await _repository.GetAsync(collectionId);
            collection.ChannelId = null;
            await _repository.UpdateAsync(collection, true);
        }
    }
}