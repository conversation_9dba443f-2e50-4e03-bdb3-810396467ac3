import{c as o}from"./chunk-MMC3E6BY.js";import{T as r,Y as i}from"./chunk-YUW2MUHJ.js";import{a as t}from"./chunk-EQDQRRRY.js";var l=class n{constructor(e){this.restService=e;this.apiName="Default";this.getChannelTree=(e,a)=>this.restService.request({method:"GET",url:"/api/app/read-only-channel/channel-tree",params:{languageCode:e}},t({apiName:this.apiName},a));this.getMatchedChannel=(e,a)=>this.restService.request({method:"GET",url:`/api/app/read-only-channel/matched-channel/${e}`},t({apiName:this.apiName},a));this.getMatchedChannelByAlbumId=(e,a)=>this.restService.request({method:"GET",url:`/api/app/read-only-channel/matched-channel-by-album-id/${e}`},t({apiName:this.apiName},a));this.getMatchedChannelByBookId=(e,a)=>this.restService.request({method:"GET",url:`/api/app/read-only-channel/matched-channel-by-book-id/${e}`},t({apiName:this.apiName},a));this.getMatchedChannelByContentCode=(e,a)=>this.restService.request({method:"GET",url:"/api/app/read-only-channel/matched-channel-by-content-code",params:{contentCode:e}},t({apiName:this.apiName},a));this.getMatchedChannelByVirtualFolderId=(e,a)=>this.restService.request({method:"GET",url:`/api/app/read-only-channel/matched-channel-by-virtual-folder-id/${e}`},t({apiName:this.apiName},a))}static{this.\u0275fac=function(a){return new(a||n)(i(o))}}static{this.\u0275prov=r({token:n,factory:n.\u0275fac,providedIn:"root"})}};export{l as a};
