import { Component, HostListener, inject, signal } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { CalendarModule } from 'primeng/calendar';
import { CardModule } from 'primeng/card';
import { PaginatorModule } from 'primeng/paginator';
import { ReadOnlyCollectionService } from '@/proxy/holy-bless/collections';
import { ActivatedRoute, Router } from '@angular/router';
import { ArticleSummaryResult } from '@/proxy/holy-bless/results';
import { Subscription } from 'rxjs';
import { I18nService } from '@/services/i18n.service';
import { MenuItem } from 'primeng/api';
import { BreadcrumbModule } from 'primeng/breadcrumb';
import { LoadingService } from '@/services/loading.service';

@Component({
  selector: 'app-summary-cards',
  standalone: true,
  imports: [
    CommonModule,
    CardModule,
    PaginatorModule,
    CalendarModule,
    ButtonModule,
    FormsModule,
    BreadcrumbModule
  ],
  providers: [DatePipe],
  templateUrl: './summary-cards.component.html',
  styleUrls: ['./summary-cards.component.scss'],
})
export class SummaryCardsComponent {
  // 面包屑导航
  home = { icon: 'pi pi-home', routerLink: '/' };
  breadcrumbItems = signal<MenuItem[]>([]);
  name = '';

  // 分页相关属性
  totalRecords = 50;
  rows = 10;
  first = 0;
  private _isMobile = false;

  // 日期选择器属性
  selectedDate: Date = new Date();
  contentCode: string | null = null;

  // 模拟数据数组
  cardItems: ArticleSummaryResult[] = [];

  #ReadOnlyCollectionService = inject(ReadOnlyCollectionService);
  #route = inject(ActivatedRoute);
  router = inject(Router);
  i18nService = inject(I18nService);
  subs = new Subscription();
  loadingService = inject(LoadingService);

  ngOnInit() {
    this.#route.queryParams.subscribe((params) => {
      if (!params['contentCode']) return;
      this.contentCode = params['contentCode'];
      this.subs.unsubscribe();
      this.subs = new Subscription();
      const sub = this.i18nService.language$.subscribe(() => {
        this.loadCollectionSummary();
      });
      this.subs.add(sub);
    });
  }
  private loadCollectionSummary() {
    if (!this.contentCode) return;
    this.loadingService.show();
    this.#ReadOnlyCollectionService
      .getCollectionSummaryByContentCode(this.contentCode, {
        skip: 0,
        maxResultCount: 10,
      })
      .subscribe({
        next: (data) => {
          this.name = data.name || '';
          this.cardItems = data.articles;
          this.totalRecords = data.totalRecords;
          this.initBreadcrumb();
          this.loadingService.hide();
        },
        error: (error) => {
          console.error('获取摘要数据失败:', error);
          this.loadingService.hide();
        },
      });
  }

  private initBreadcrumb() {
    const items: MenuItem[] = [
      {
        label: this.name,
      },
    ];

    this.breadcrumbItems.set(items);
  }

  navigateToArticle(item: ArticleSummaryResult) {
    if (!item.id) return;
    this.router.navigateByUrl(
      `/home/<USER>/${item.id}`,
    );
  }

  constructor() {
    this.checkMobile();
  }

  // 检测是否为移动端
  get isMobile(): boolean {
    return this._isMobile;
  }

  // 根据设备类型返回每页条数选项
  get rowsPerPageOptions(): number[] | undefined {
    return this.isMobile ? undefined : [10, 20, 50];
  }

  // 监听窗口大小变化
  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.checkMobile();
  }

  private checkMobile() {
    this._isMobile = window.innerWidth <= 768;
  }

  // 分页事件处理
  onPageChange(event: any) {
    this.first = event.first;
    this.rows = event.rows;
    console.log('页面变化:', event);
    // 这里可以添加数据加载逻辑
  }

  // 日期选择事件处理
  onDateChange(event: Date) {
    console.log('选择的日期:', event);
    // 这里可以添加按日期筛选数据的逻辑
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
