{"id": "132d56a5-224a-4edf-b32f-07f766123925", "template": "app", "versions": {"AbpFramework": "9.2.3", "AbpStudio": "1.1.2", "TargetDotnetFramework": "net9.0", "AbpCommercial": "9.2.3"}, "modules": {"HolyBless": {"path": "HolyBless.abpmdl"}}, "runProfiles": {"Default": {"path": "etc/abp-studio/run-profiles/Default.abprun.json"}}, "options": {"httpRequests": {"ignoredUrls": ["^/metrics$"]}}, "creatingStudioConfiguration": {"template": "app", "createdAbpStudioVersion": "0.9.19", "tiered": "false", "runInstallLibs": "true", "useLocalReferences": "false", "multiTenancy": "false", "includeTests": "true", "kubernetesConfiguration": "false", "uiFramework": "angular", "mobileFramework": "none", "distributedEventBus": "none", "databaseProvider": "ef", "runDbMigrator": "true", "databaseManagementSystem": "postgresql", "separateTenantSchema": "false", "createInitialMigration": "true", "theme": "basic", "themeStyle": "", "progressiveWebApp": "true", "runProgressiveWebAppSupport": "true", "publicWebsite": "false", "socialLogin": ""}}