<div class="flex flex-1">
  <div class="articaldetail-container prose max-w-none p-6 flex-1">
    <h1 class="text-3xl font-bold mb-4">{{articleDetail()?.title}}</h1>
    <p class="text-sm mb-4 flex items-center gap-2">
      @if (articleDetail()) {
      <i class="pi pi-clock"></i>
      {{ articleDetail()?.deliveryDate | date: 'yyyy-MM-dd HH:mm:ss' }} }
    </p>
    <div class="mb-4" [innerHTML]="articleDetail()?.content"></div>
  </div>
  <div class="p-6">
    <div>
      <p-galleria
        [value]="imageArticleFiles()"
        indicatorsPosition="right"
        [showIndicators]="true"
        [showThumbnails]="false"
        [showIndicatorsOnItem]="true"
        [containerStyle]="{'width': '100%','margin-top': '2em'}"
      >
        <ng-template pTemplate="item" let-item>
          <img
            [src]="item.fileUrl"
            class="max-w-full max-h-full w-auto h-auto rounded-lg object-cover shadow-lg"
          />
        </ng-template>
      </p-galleria>
    </div>
    <div>
      <div class="mt-6">
        @if (primaryArticleFiles().length > 0) {
        <h3>{{i18nService.translate('media_playback')}}</h3>
        @for (item of primaryArticleFiles(); track $index) {
        <p class="flex justify-between items-center mt-3">
          <span>{{ item.fileName | removeExtension }}</span>
          <i
            class="pi pi-play-circle cursor-pointer"
            (click)="playMedia(item)"
          ></i>
        </p>
        } }
      </div>
      <div class="mt-6">
        @if (notImageArticleFiles().length > 0) {
        <h3>{{i18nService.translate('attachment_download')}}</h3>
        @for (item of notImageArticleFiles(); track $index) {
        <p class="flex justify-between items-center mt-3">
          <span>{{ item.fileName | removeExtension }}</span>
          <i
            class="pi pi-cloud-download cursor-pointer"
            [appDownload]="item.fileUrl!"
            [downloadName]="item.fileName"
          ></i>
        </p>
        } }
      </div>
    </div>
  </div>
</div>
