import{a as J,b as K}from"./chunk-4UYMFPPF.js";import"./chunk-OLNEY35K.js";import{a as $}from"./chunk-JWVI4R37.js";import{a as q}from"./chunk-43MY25HH.js";import"./chunk-7SYDZWIG.js";import{b as j,c as z}from"./chunk-TAPTBL76.js";import{a as U}from"./chunk-QJAYFQIR.js";import{a as G}from"./chunk-MJMCW5UB.js";import{a as W}from"./chunk-Y2PVWY7X.js";import"./chunk-MMC3E6BY.js";import"./chunk-KB23BJ64.js";import"./chunk-QAUHHFOT.js";import"./chunk-VBHJ4LBO.js";import"./chunk-P4BOZY2U.js";import"./chunk-OTT6DUE3.js";import"./chunk-22JWGO27.js";import{ba as L}from"./chunk-SXMRENJM.js";import"./chunk-BMA7WWEI.js";import{D as k,G as H,i as R,k as B}from"./chunk-GDGXRFMB.js";import{$b as M,Ab as l,Ac as u,Bb as p,Kb as I,Lb as g,Oa as x,Pa as F,Ta as n,Vb as c,Wb as _,Xb as E,Z as s,Zb as D,_b as N,a as y,bc as O,cb as w,ib as d,kc as v,lc as A,mc as P,qb as m,ta as b,vb as h,wb as C,xb as T,yb as S,zb as o}from"./chunk-YUW2MUHJ.js";import"./chunk-EQDQRRRY.js";var V=()=>({width:"100%","margin-top":"2em"});function X(t,e){if(t&1&&(p(0,"i",10),c(1),v(2,"date")),t&2){let i,r=g();n(),E(" ",P(2,1,(i=r.articleDetail())==null?null:i.deliveryDate,"yyyy-MM-dd HH:mm:ss")," ")}}function Y(t,e){if(t&1&&p(0,"img",11),t&2){let i=e.$implicit;m("src",i.fileUrl,F)}}function Z(t,e){if(t&1&&(o(0,"p",12)(1,"span"),c(2),v(3,"removeExtension"),l(),p(4,"i",13),l()),t&2){let i=e.$implicit;n(2),_(A(3,1,i.fileName))}}function ee(t,e){if(t&1&&(o(0,"h3"),c(1,"\u5A92\u4F53\u64AD\u653E"),l(),T(2,Z,5,3,"p",12,C)),t&2){let i=g();n(2),S(i.primaryArticleFiles())}}function ie(t,e){if(t&1&&(o(0,"p",12)(1,"span"),c(2),v(3,"removeExtension"),l(),p(4,"i",14),l()),t&2){let i=e.$implicit;n(2),_(A(3,1,i.fileName))}}function te(t,e){if(t&1&&(o(0,"h3"),c(1,"\u9644\u4EF6\u4E0B\u8F7D"),l(),T(2,ie,5,3,"p",12,C)),t&2){let i=g();n(2),S(i.notImageArticleFiles())}}var Q=class t{constructor(){this.#e=s(U);this.#i=s($);this.#t=s(k);this.i18nService=s(W);this.subs=new y;this.loadingService=s(G);this.router=s(H);this.contentCode=null;this.files=[];this.articleDetail=b(null);this.articleFiles=u(()=>this.articleDetail()?.articleFiles||[]);this.primaryArticleFiles=u(()=>this.articleFiles().filter(e=>e.isPrimary===!0));this.imageArticleFiles=u(()=>this.articleFiles().filter(e=>e.mediaType===0));this.notImageArticleFiles=u(()=>this.articleFiles().filter(e=>e.mediaType!==0))}#e;#i;#t;ngOnInit(){this.#t.queryParams.subscribe(e=>{if(!e.contentCode)return;this.contentCode=e.contentCode,this.subs.unsubscribe(),this.subs=new y;let i=this.i18nService.language$.subscribe(()=>{this.loadCollectionSummary()});this.subs.add(i)})}loadCollectionSummary(){this.articleDetail.set(null),this.loadingService.show(),this.#e.getCollectionTreeAndArticleTitlesByContentCode(this.contentCode).subscribe({next:e=>{if(this.loadingService.hide(),!e||e.length===0){this.router.navigateByUrl("/landing");return}this.files=e.map(i=>this.buildTreeNode(i))},error:e=>{console.error("\u83B7\u53D6\u6587\u7AE0\u6811\u6570\u636E\u5931\u8D25:",e),this.loadingService.hide()}})}buildTreeNode(e){return{key:e.id,label:e.name||e.title,data:e,children:e.articles?e.articles.map(i=>this.buildTreeNode(i)):[]}}loadArticalDetail(e){e.key&&(this.loadingService.show(),this.#i.getArticleAggregate(+e.key).subscribe({next:i=>{this.articleDetail.set(i),this.loadingService.hide()},error:i=>{console.error("\u83B7\u53D6\u6587\u7AE0\u8BE6\u60C5\u5931\u8D25:",i),this.loadingService.hide()}}))}ngOnDestroy(){this.subs.unsubscribe()}static{this.\u0275fac=function(i){return new(i||t)}}static{this.\u0275cmp=w({type:t,selectors:[["app-artical-tree"]],decls:17,vars:15,consts:[[1,"flex","flex-1"],["styleClass","w-[20rem] h-full","selectionMode","single","virtualScrollItemSize","36",3,"selectionChange","onNodeSelect","value","selection","virtualScroll","filter"],[1,"articaldetail-container","prose","max-w-none","p-6","flex-1"],[1,"text-3xl","font-bold","mb-4"],[1,"text-sm","mb-4","flex","items-center","gap-2"],[1,"mb-4",3,"innerHTML"],[1,"p-6"],["indicatorsPosition","right",3,"value","showIndicators","showThumbnails","showIndicatorsOnItem","containerStyle"],["pTemplate","item"],[1,"mt-6"],[1,"pi","pi-clock"],[1,"w-72","h-96","rounded-lg","shadow-lg",3,"src"],[1,"flex","justify-between","items-center","mt-3"],[1,"pi","pi-play-circle"],[1,"pi","pi-cloud-download"]],template:function(i,r){if(i&1&&(o(0,"div",0)(1,"p-tree",1),M("selectionChange",function(a){return N(r.selectedFile,a)||(r.selectedFile=a),a}),I("onNodeSelect",function(a){return r.loadArticalDetail(a.node)}),l(),o(2,"div",2)(3,"h1",3),c(4),l(),o(5,"p",4),d(6,X,3,4),l(),p(7,"p",5),l(),o(8,"div",6)(9,"div")(10,"p-galleria",7),d(11,Y,1,1,"ng-template",8),l()(),o(12,"div")(13,"div",9),d(14,ee,4,0),l(),o(15,"div",9),d(16,te,4,0),l()()()()),i&2){let f,a;n(),m("value",r.files),D("selection",r.selectedFile),m("virtualScroll",!0)("filter",!0),n(3),_((f=r.articleDetail())==null?null:f.title),n(2),h(r.articleDetail()?6:-1),n(),m("innerHTML",(a=r.articleDetail())==null?null:a.content,x),n(3),m("value",r.imageArticleFiles())("showIndicators",!0)("showThumbnails",!1)("showIndicatorsOnItem",!0)("containerStyle",O(14,V)),n(4),h(r.primaryArticleFiles().length>0?14:-1),n(2),h(r.notImageArticleFiles().length>0?16:-1)}},dependencies:[B,R,K,J,L,z,j,q],styles:["[_nghost-%COMP%]{flex:1;display:flex}[_nghost-%COMP%]     .p-virtualscroller{height:calc(100% - 30px)!important}"]})}};export{Q as ArticleTreeComponent};
