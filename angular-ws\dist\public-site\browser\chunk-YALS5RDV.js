import{a as H}from"./chunk-2C3NUNTC.js";import{a as E}from"./chunk-EYER3BI4.js";import{a as $}from"./chunk-N5RHZG25.js";import{a as B}from"./chunk-MJMCW5UB.js";import{a as x}from"./chunk-Y2PVWY7X.js";import"./chunk-MMC3E6BY.js";import{b as D}from"./chunk-QPTTFFWZ.js";import{c as O,d as j}from"./chunk-4JDMXTQG.js";import"./chunk-XXVSVAEK.js";import"./chunk-P4BOZY2U.js";import"./chunk-OTT6DUE3.js";import"./chunk-22JWGO27.js";import"./chunk-SXMRENJM.js";import"./chunk-BMA7WWEI.js";import{D as P,G as T,M as k,h as L,k as R}from"./chunk-GDGXRFMB.js";import{Ab as l,Bb as f,Fb as V,Kb as h,Lb as I,Oa as y,T as C,Ta as d,Z as o,a as u,cb as _,fa as S,g as b,ga as w,kc as g,lc as v,qb as p,ta as s,xb as F,yb as M,zb as a}from"./chunk-YUW2MUHJ.js";import"./chunk-EQDQRRRY.js";var c=class r{constructor(){this.#e=new b(!1);this.isMobile=this.#e.asObservable();this.#i(),window.addEventListener("resize",()=>this.#i())}#e;#i(){this.#e.next(window.innerWidth<=768)}static{this.\u0275fac=function(i){return new(i||r)}}static{this.\u0275prov=C({token:r,factory:r.\u0275fac,providedIn:"root"})}};var A=(r,e)=>e.id;function U(r,e){if(r&1){let i=V();a(0,"div",5),h("click",function(){let n=S(i).$implicit,m=I();return w(m.navigateToFolderDetail(n.id))}),a(1,"p",6),f(2,"img",7),l(),f(3,"p",8),l()}if(r&2){let i=e.$implicit;d(3),p("innerHTML",i.folderName,y)}}var N=class r{constructor(){this.#e=o(H);this.#i=o($);this.mobileService=o(c);this.router=o(T);this.route=o(P);this.i18nService=o(x);this.channelIdContentCodeService=o(E);this.subs=new u;this.loadingService=o(B);this.first=s(0);this.rows=s(10);this.totalRecords=s(0);this.items=s([]);this.channelId=void 0}#e;#i;ngOnInit(){this.route.params.subscribe(e=>{let i=e.channelId;this.channelId=i,i&&(this.changeLanguage(i),this.loadVirtualFolders())})}changeLanguage(e){this.subs.unsubscribe(),this.subs=new u;let i=this.i18nService.language$.subscribe(()=>{let t=this.channelIdContentCodeService.getChannelIdContentCode(e);t&&this.#i.getMatchedChannelByContentCode(t).subscribe({next:n=>{n.id!==e&&this.router.navigateByUrl(`/virtual-folder/list/${n.id}`)}})});this.subs.add(i)}loadVirtualFolders(){this.channelId&&(this.loadingService.show(),this.#e.getList({channelId:this.channelId,skipCount:this.first(),maxResultCount:this.rows()}).subscribe({next:e=>{this.items.set(e.items||[]),this.totalRecords.set(e.totalCount||0),this.loadingService.hide()},error:e=>{console.error("\u83B7\u53D6\u865A\u62DF\u6587\u4EF6\u5939\u5217\u8868\u5931\u8D25:",e),this.loadingService.hide()}}))}onPageChange(e){this.first.set(e.first),this.rows.set(e.rows),this.loadVirtualFolders()}navigateToFolderDetail(e){e&&this.router.navigateByUrl(`/virtual-folder/folder-detail/${e}`)}ngOnDestroy(){this.subs.unsubscribe()}static{this.\u0275fac=function(i){return new(i||r)}}static{this.\u0275cmp=_({type:r,selectors:[["app-virtual-folder-list"]],decls:8,vars:9,consts:[[1,"p-6"],[1,"grid","grid-cols-1","md:grid-cols-2","lg:grid-cols-5","gap-6","mb-8"],[1,"cursor-pointer"],[1,"pagination-container"],["styleClass","custom-paginator",3,"onPageChange","first","rows","totalRecords","showPageLinks","showCurrentPageReport"],[1,"cursor-pointer",3,"click"],[1,"flex","justify-center","mb-4"],["src","assets/images/folder.png","alt",""],[1,"flex","justify-center",3,"innerHTML"]],template:function(i,t){i&1&&(a(0,"div",0)(1,"div",1),F(2,U,4,1,"div",2,A),l(),a(4,"div",3)(5,"p-paginator",4),g(6,"async"),g(7,"async"),h("onPageChange",function(m){return t.onPageChange(m)}),l()()()),i&2&&(d(2),M(t.items()),d(3),p("first",t.first())("rows",t.rows())("totalRecords",t.totalRecords())("showPageLinks",!v(6,5,t.mobileService.isMobile))("showCurrentPageReport",v(7,7,t.mobileService.isMobile)))},dependencies:[R,L,k,D,j,O],styles:["[_nghost-%COMP%]{flex:1}"]})}};export{N as VirtualFolderListComponent};
