import { Component, HostListener, inject, signal } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { CardModule } from 'primeng/card';
import { PaginatorModule } from 'primeng/paginator';
import { CalendarModule } from 'primeng/calendar';
import { ButtonModule } from 'primeng/button';
import { FormsModule } from '@angular/forms';
import { ReadOnlyCollectionService } from '@/proxy/holy-bless/collections';
import { ActivatedRoute, Router } from '@angular/router';
import { ArticleSummaryResult } from '@/proxy/holy-bless/results';
import { TranslatePipe } from '@/pipes/translate.pipe';
import { I18nService } from '@/services/i18n.service';
import { Subscription } from 'rxjs';
import { BreadcrumbModule } from 'primeng/breadcrumb';
import { MenuItem } from 'primeng/api';
import { LoadingService } from '@/services/loading.service';
import { DatePickerModule } from 'primeng/datepicker';

@Component({
  selector: 'app-image-cards',
  standalone: true,
  imports: [
    CommonModule,
    CardModule,
    PaginatorModule,
    CalendarModule,
    ButtonModule,
    FormsModule,
    BreadcrumbModule,
    DatePickerModule
  ],
  providers: [DatePipe],
  templateUrl: './image-cards.component.html',
  styleUrls: ['./image-cards.component.scss'],
})
export class ImageCardsComponent {
  home: MenuItem = { icon: 'pi pi-home', routerLink: '/' };
  breadcrumbItems = signal<MenuItem[]>([]);
  // 分页相关属性
  totalRecords = 0;
  rows = 10;
  first = 0;
  isMobile = false;

  selectedDate: Date | null = null;
  contentCode: string | null = null;

  #ReadOnlyCollectionService = inject(ReadOnlyCollectionService);
  i18nService = inject(I18nService);
  #route = inject(ActivatedRoute);
  router = inject(Router);
  subs = new Subscription();
  loadingService = inject(LoadingService);

  cardItems: ArticleSummaryResult[] = [];

  name = '';

  ngOnInit() {
    this.#route.queryParams.subscribe((params) => {
      if (params['contentCode']) {
        this.contentCode = params['contentCode'];
        this.subs.unsubscribe();
        this.subs = new Subscription();
        const sub = this.i18nService.language$.subscribe(() => {
          this.loadCollectionSummary();
        });
        this.subs.add(sub);
      }
    });
  }

  private initBreadcrumb() {
    const items: MenuItem[] = [
      {
        label: this.name,
      },
    ];

    this.breadcrumbItems.set(items);
  }

  private loadCollectionSummary() {
    if (!this.contentCode) return;
    this.loadingService.show();
    this.#ReadOnlyCollectionService
      .getCollectionSummaryByContentCode(this.contentCode, {
        skip: this.first,
        maxResultCount: this.rows,
        year: this.selectedDate?.getFullYear(),
        month: (this.selectedDate?.getMonth() || 0) + 1,
      })
      .subscribe({
        next: (data) => {
          this.name = data.name || '';
          this.cardItems = data.articles;
          this.totalRecords = data.totalRecords;
          this.initBreadcrumb();
          this.loadingService.hide();
        },
        error: (error) => {
          console.error('获取摘要数据失败:', error);
          this.loadingService.hide();
        },
      });
  }

  navigateToArticle(item: ArticleSummaryResult) {
    if (!item.id) return;
    this.router.navigateByUrl(`/home/<USER>/${item.id}`);
  }

  onPageChange(event: any) {
    this.first = event.first;
    this.rows = event.rows;
    this.loadCollectionSummary();
  }

  onDateChange(event: Date | null) {
    this.selectedDate = event;
    this.loadCollectionSummary();
  }

  constructor() {
    this.checkMobile();
  }

  // 根据设备类型返回每页条数选项
  get rowsPerPageOptions(): number[] | undefined {
    return this.isMobile ? undefined : [10];
  }

  // 监听窗口大小变化
  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.checkMobile();
  }

  private checkMobile() {
    this.isMobile = window.innerWidth <= 768;
  }

  // 播放本页
  playCurrentPage() {}

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
