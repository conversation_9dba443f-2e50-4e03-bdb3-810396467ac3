import{Kb as a,eb as i}from"./chunk-YUW2MUHJ.js";var d=class t{onClick(){if(this.downloadUrl)try{let o=document.createElement("a");o.href=this.downloadUrl,o.download=this.downloadName||this.extractFileName()||"download",document.body.appendChild(o),o.click(),document.body.removeChild(o)}catch(o){console.error("Download failed:",o),window.open(this.downloadUrl,"_blank")}}extractFileName(){try{return new URL(this.downloadUrl).pathname.split("/").pop()||"download"}catch{return this.downloadUrl.split("/").pop()||"download"}}static{this.\u0275fac=function(n){return new(n||t)}}static{this.\u0275dir=i({type:t,selectors:[["","appDownload",""]],hostBindings:function(n,e){n&1&&a("click",function(){return e.onClick()})},inputs:{downloadUrl:[0,"appDownload","downloadUrl"],downloadName:"downloadName"}})}};export{d as a};
