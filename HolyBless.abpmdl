{"template": "app", "imports": {"Volo.Abp.BasicTheme": {"version": "9.2.3", "isInstalled": true}, "Volo.Abp.Account": {"version": "9.2.3", "isInstalled": true}, "Volo.Abp.OpenIddict": {"version": "9.2.3", "isInstalled": true}, "Volo.Abp.Identity": {"version": "9.2.3", "isInstalled": true}, "Volo.Abp.SettingManagement": {"version": "9.2.3", "isInstalled": true}, "Volo.Abp.PermissionManagement": {"version": "9.2.3", "isInstalled": true}, "Volo.Abp.FeatureManagement": {"version": "9.2.3", "isInstalled": true}}, "folders": {"items": {"src": {}, "test": {}}}, "packages": {"HolyBless.Application": {"path": "src/HolyBless.Application/HolyBless.Application.abppkg", "folder": "src"}, "HolyBless.Application.Tests": {"path": "test/HolyBless.Application.Tests/HolyBless.Application.Tests.abppkg", "folder": "test"}, "HolyBless.Domain.Shared": {"path": "src/HolyBless.Domain.Shared/HolyBless.Domain.Shared.abppkg", "folder": "src"}, "HolyBless.Application.Contracts": {"path": "src/HolyBless.Application.Contracts/HolyBless.Application.Contracts.abppkg", "folder": "src"}, "HolyBless.HttpApi": {"path": "src/HolyBless.HttpApi/HolyBless.HttpApi.abppkg", "folder": "src"}, "HolyBless.HttpApi.Client": {"path": "src/HolyBless.HttpApi.Client/HolyBless.HttpApi.Client.abppkg", "folder": "src"}, "HolyBless.EntityFrameworkCore.Tests": {"path": "test/HolyBless.EntityFrameworkCore.Tests/HolyBless.EntityFrameworkCore.Tests.abppkg", "folder": "test"}, "HolyBless.EntityFrameworkCore": {"path": "src/HolyBless.EntityFrameworkCore/HolyBless.EntityFrameworkCore.abppkg", "folder": "src"}, "HolyBless.TestBase": {"path": "test/HolyBless.TestBase/HolyBless.TestBase.abppkg", "folder": "test"}, "HolyBless.Domain.Tests": {"path": "test/HolyBless.Domain.Tests/HolyBless.Domain.Tests.abppkg", "folder": "test"}, "HolyBless.HttpApi.Client.ConsoleTestApp": {"path": "test/HolyBless.HttpApi.Client.ConsoleTestApp/HolyBless.HttpApi.Client.ConsoleTestApp.abppkg", "folder": "test"}, "HolyBless.DbMigrator": {"path": "src/HolyBless.DbMigrator/HolyBless.DbMigrator.abppkg", "folder": "src"}, "HolyBless.HttpApi.Host": {"path": "src/HolyBless.HttpApi.Host/HolyBless.HttpApi.Host.abppkg", "folder": "src"}, "HolyBless.Domain": {"path": "src/HolyBless.Domain/HolyBless.Domain.abppkg", "folder": "src"}}}