import{c as l}from"./chunk-MMC3E6BY.js";import{T as a,Y as r}from"./chunk-YUW2MUHJ.js";import{a as o}from"./chunk-EQDQRRRY.js";var n=class i{constructor(t){this.restService=t;this.apiName="Default";this.get=(t,e)=>this.restService.request({method:"GET",url:`/api/app/read-only-collection/${t}`},o({apiName:this.apiName},e));this.getCollectionArticleTitlesByContentCode=(t,e)=>this.restService.request({method:"GET",url:"/api/app/read-only-collection/collection-article-titles-by-content-code",params:{contentCode:t}},o({apiName:this.apiName},e));this.getCollectionSummaryByContentCode=(t,e,c)=>this.restService.request({method:"GET",url:"/api/app/read-only-collection/collection-summary-by-content-code",params:{contentCode:t,skip:e.skip,maxResultCount:e.maxResultCount,sorting:e.sorting,year:e.year,month:e.month}},o({apiName:this.apiName},c));this.getCollectionTreeAndArticleTitlesByContentCode=(t,e)=>this.restService.request({method:"GET",url:"/api/app/read-only-collection/collection-tree-and-article-titles-by-content-code",params:{contentCode:t}},o({apiName:this.apiName},e));this.getCollectionTreeByContentCode=(t,e)=>this.restService.request({method:"GET",url:"/api/app/read-only-collection/collection-tree-by-content-code",params:{contentCode:t}},o({apiName:this.apiName},e));this.getFirstByChannelId=(t,e)=>this.restService.request({method:"GET",url:`/api/app/read-only-collection/first-by-channel-id/${t}`},o({apiName:this.apiName},e));this.getLanguageMatchingCollection=(t,e)=>this.restService.request({method:"GET",url:`/api/app/read-only-collection/language-matching-collection/${t}`},o({apiName:this.apiName},e));this.getLanguageMatchingCollectionByContentCode=(t,e)=>this.restService.request({method:"GET",url:"/api/app/read-only-collection/language-matching-collection-by-content-code",params:{contentCode:t}},o({apiName:this.apiName},e))}static{this.\u0275fac=function(e){return new(e||i)(r(l))}}static{this.\u0275prov=a({token:i,factory:i.\u0275fac,providedIn:"root"})}};export{n as a};
