import{ua as En,va as bn}from"./chunk-SXMRENJM.js";import{B as Vr,F as Tn,G as wn,M as Sn,a as Ve,k as mn,n as pn,q as gn,r as $r,s as zr,t as _r,u as Ue,v as jr,w as Lt,y as yn,z as vn}from"./chunk-GDGXRFMB.js";import{A as Mt,Ac as hn,Bc as xt,E as Ct,F as on,N as ee,P as Te,Q as an,T as I,U as _e,W as C,Y as S,Ya as xr,Z as N,_ as un,da as cn,db as je,f as ze,fb as Lr,g as B,h as nn,ia as ln,j as Nr,k as kt,ka as K,l as Dt,la as Nt,mb as dn,o as Fr,p as rn,q as E,r as sn,rc as Ar,sc as Rr,ta as Ft,z as ce,zc as fn}from"./chunk-YUW2MUHJ.js";import{a as l,b,c as ue,f as tn}from"./chunk-EQDQRRRY.js";function Ds(n,e){!e?.injector&&cn(Ds);let t=e?.injector??N(K),r=new nn(1),i=xt(()=>{let s;try{s=n()}catch(o){fn(()=>r.error(o));return}fn(()=>r.next(s))},{injector:t,manualCleanup:!0});return t.get(Nt).onDestroy(()=>{i.destroy(),r.complete()}),r.asObservable()}function In(n,e){let t=!e?.manualCleanup;t&&!e?.injector&&cn(In);let r=t?e?.injector?.get(Nt)??N(Nt):null,i=Ms(e?.equal),s;e?.requireSync?s=Ft({kind:0},{equal:i}):s=Ft({kind:1,value:e?.initialValue},{equal:i});let o,a=n.subscribe({next:u=>s.set({kind:1,value:u}),error:u=>{if(e?.rejectErrors)throw u;s.set({kind:2,error:u})},complete:()=>{o?.()}});if(e?.requireSync&&s().kind===0)throw new an(601,!1);return o=r?.onDestroy(a.unsubscribe.bind(a)),hn(()=>{let u=s();switch(u.kind){case 1:return u.value;case 2:throw u.error;case 0:throw new an(601,!1)}},{equal:e?.equal})}function Ms(n=Object.is){return(e,t)=>e.kind===1&&t.kind===1&&n(e.value,t.value)}var On=kn;function kn(n,e){return n===e||n!==n&&e!==e?!0:typeof n!=typeof e||{}.toString.call(n)!={}.toString.call(e)||n!==Object(n)||!n?!1:Array.isArray(n)?Ur(n,e):{}.toString.call(n)=="[object Set]"?Ur(Array.from(n),Array.from(e)):{}.toString.call(n)=="[object Object]"?Ns(n,e):Cs(n,e)}function Cs(n,e){return n.toString()===e.toString()}function Ur(n,e){var t=n.length;if(t!=e.length)return!1;for(var r=0;r<t;r++)if(!kn(n[r],e[r]))return!1;return!0}function Ns(n,e){var t=Object.keys(n),r=t.length;if(r!=Object.keys(e).length)return!1;for(var i=0;i<r;i++){var s=t[i];if(!(e.hasOwnProperty(s)&&kn(n[s],e[s])))return!1}return!0}var Wr=We;function We(n){let e=n;var t={}.toString.call(n).slice(8,-1);if(t=="Set")return new Set([...n].map(i=>We(i)));if(t=="Map")return new Map([...n].map(i=>[We(i[0]),We(i[1])]));if(t=="Date")return new Date(n.getTime());if(t=="RegExp")return RegExp(n.source,Fs(n));if(t=="Array"||t=="Object"){e=Array.isArray(n)?[]:{};for(var r in n)e[r]=We(n[r])}return e}function Fs(n){if(typeof n.source.flags=="string")return n.source.flags;var e=[];return n.global&&e.push("g"),n.ignoreCase&&e.push("i"),n.multiline&&e.push("m"),n.sticky&&e.push("y"),n.unicode&&e.push("u"),e.join("")}var J=class extends Error{},At=class extends J{constructor(e){super(`Invalid DateTime: ${e.toMessage()}`)}},Rt=class extends J{constructor(e){super(`Invalid Interval: ${e.toMessage()}`)}},$t=class extends J{constructor(e){super(`Invalid Duration: ${e.toMessage()}`)}},W=class extends J{},we=class extends J{constructor(e){super(`Invalid unit ${e}`)}},k=class extends J{},P=class extends J{constructor(){super("Zone is an abstract class")}};var f="numeric",H="short",R="long",te={year:f,month:f,day:f},Pe={year:f,month:H,day:f},Dn={year:f,month:H,day:f,weekday:H},He={year:f,month:R,day:f},Ze={year:f,month:R,day:f,weekday:R},Ge={hour:f,minute:f},qe={hour:f,minute:f,second:f},Ye={hour:f,minute:f,second:f,timeZoneName:H},Be={hour:f,minute:f,second:f,timeZoneName:R},Ke={hour:f,minute:f,hourCycle:"h23"},Je={hour:f,minute:f,second:f,hourCycle:"h23"},Qe={hour:f,minute:f,second:f,hourCycle:"h23",timeZoneName:H},Xe={hour:f,minute:f,second:f,hourCycle:"h23",timeZoneName:R},et={year:f,month:f,day:f,hour:f,minute:f},tt={year:f,month:f,day:f,hour:f,minute:f,second:f},nt={year:f,month:H,day:f,hour:f,minute:f},rt={year:f,month:H,day:f,hour:f,minute:f,second:f},Mn={year:f,month:H,day:f,weekday:H,hour:f,minute:f},it={year:f,month:R,day:f,hour:f,minute:f,timeZoneName:H},st={year:f,month:R,day:f,hour:f,minute:f,second:f,timeZoneName:H},ot={year:f,month:R,day:f,weekday:R,hour:f,minute:f,timeZoneName:R},at={year:f,month:R,day:f,weekday:R,hour:f,minute:f,second:f,timeZoneName:R};var $=class{get type(){throw new P}get name(){throw new P}get ianaName(){return this.name}get isUniversal(){throw new P}offsetName(e,t){throw new P}formatOffset(e,t){throw new P}offset(e){throw new P}equals(e){throw new P}get isValid(){throw new P}};var Cn=null,le=class n extends ${static get instance(){return Cn===null&&(Cn=new n),Cn}get type(){return"system"}get name(){return new Intl.DateTimeFormat().resolvedOptions().timeZone}get isUniversal(){return!1}offsetName(e,{format:t,locale:r}){return _t(e,t,r)}formatOffset(e,t){return ne(this.offset(e),t)}offset(e){return-new Date(e).getTimezoneOffset()}equals(e){return e.type==="system"}get isValid(){return!0}};var Fn=new Map;function xs(n){let e=Fn.get(n);return e===void 0&&(e=new Intl.DateTimeFormat("en-US",{hour12:!1,timeZone:n,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",era:"short"}),Fn.set(n,e)),e}var Ls={year:0,month:1,day:2,era:3,hour:4,minute:5,second:6};function As(n,e){let t=n.format(e).replace(/\u200E/g,""),r=/(\d+)\/(\d+)\/(\d+) (AD|BC),? (\d+):(\d+):(\d+)/.exec(t),[,i,s,o,a,u,c,d]=r;return[o,i,s,a,u,c,d]}function Rs(n,e){let t=n.formatToParts(e),r=[];for(let i=0;i<t.length;i++){let{type:s,value:o}=t[i],a=Ls[s];s==="era"?r[a]=o:p(a)||(r[a]=parseInt(o,10))}return r}var Nn=new Map,A=class n extends ${static create(e){let t=Nn.get(e);return t===void 0&&Nn.set(e,t=new n(e)),t}static resetCache(){Nn.clear(),Fn.clear()}static isValidSpecifier(e){return this.isValidZone(e)}static isValidZone(e){if(!e)return!1;try{return new Intl.DateTimeFormat("en-US",{timeZone:e}).format(),!0}catch{return!1}}constructor(e){super(),this.zoneName=e,this.valid=n.isValidZone(e)}get type(){return"iana"}get name(){return this.zoneName}get isUniversal(){return!1}offsetName(e,{format:t,locale:r}){return _t(e,t,r,this.name)}formatOffset(e,t){return ne(this.offset(e),t)}offset(e){if(!this.valid)return NaN;let t=new Date(e);if(isNaN(t))return NaN;let r=xs(this.name),[i,s,o,a,u,c,d]=r.formatToParts?Rs(r,t):As(r,t);a==="BC"&&(i=-Math.abs(i)+1);let g=Se({year:i,month:s,day:o,hour:u===24?0:u,minute:c,second:d,millisecond:0}),m=+t,M=m%1e3;return m-=M>=0?M:1e3+M,(g-m)/(60*1e3)}equals(e){return e.type==="iana"&&e.name===this.name}get isValid(){return this.valid}};var Pr={};function $s(n,e={}){let t=JSON.stringify([n,e]),r=Pr[t];return r||(r=new Intl.ListFormat(n,e),Pr[t]=r),r}var xn=new Map;function Ln(n,e={}){let t=JSON.stringify([n,e]),r=xn.get(t);return r===void 0&&(r=new Intl.DateTimeFormat(n,e),xn.set(t,r)),r}var An=new Map;function zs(n,e={}){let t=JSON.stringify([n,e]),r=An.get(t);return r===void 0&&(r=new Intl.NumberFormat(n,e),An.set(t,r)),r}var Rn=new Map;function _s(n,e={}){let o=e,{base:t}=o,r=ue(o,["base"]),i=JSON.stringify([n,r]),s=Rn.get(i);return s===void 0&&(s=new Intl.RelativeTimeFormat(n,e),Rn.set(i,s)),s}var ut=null;function js(){return ut||(ut=new Intl.DateTimeFormat().resolvedOptions().locale,ut)}var $n=new Map;function Hr(n){let e=$n.get(n);return e===void 0&&(e=new Intl.DateTimeFormat(n).resolvedOptions(),$n.set(n,e)),e}var zn=new Map;function Vs(n){let e=zn.get(n);if(!e){let t=new Intl.Locale(n);e="getWeekInfo"in t?t.getWeekInfo():t.weekInfo,"minimalDays"in e||(e=l(l({},Zr),e)),zn.set(n,e)}return e}function Us(n){let e=n.indexOf("-x-");e!==-1&&(n=n.substring(0,e));let t=n.indexOf("-u-");if(t===-1)return[n];{let r,i;try{r=Ln(n).resolvedOptions(),i=n}catch{let u=n.substring(0,t);r=Ln(u).resolvedOptions(),i=u}let{numberingSystem:s,calendar:o}=r;return[i,s,o]}}function Ws(n,e,t){return(t||e)&&(n.includes("-u-")||(n+="-u"),t&&(n+=`-ca-${t}`),e&&(n+=`-nu-${e}`)),n}function Ps(n){let e=[];for(let t=1;t<=12;t++){let r=v.utc(2009,t,1);e.push(n(r))}return e}function Hs(n){let e=[];for(let t=1;t<=7;t++){let r=v.utc(2016,11,13+t);e.push(n(r))}return e}function jt(n,e,t,r){let i=n.listingMode();return i==="error"?null:i==="en"?t(e):r(e)}function Zs(n){return n.numberingSystem&&n.numberingSystem!=="latn"?!1:n.numberingSystem==="latn"||!n.locale||n.locale.startsWith("en")||Hr(n.locale).numberingSystem==="latn"}var _n=class{constructor(e,t,r){this.padTo=r.padTo||0,this.floor=r.floor||!1;let a=r,{padTo:i,floor:s}=a,o=ue(a,["padTo","floor"]);if(!t||Object.keys(o).length>0){let u=l({useGrouping:!1},r);r.padTo>0&&(u.minimumIntegerDigits=r.padTo),this.inf=zs(e,u)}}format(e){if(this.inf){let t=this.floor?Math.floor(e):e;return this.inf.format(t)}else{let t=this.floor?Math.floor(e):Ee(e,3);return O(t,this.padTo)}}},jn=class{constructor(e,t,r){this.opts=r,this.originalZone=void 0;let i;if(this.opts.timeZone)this.dt=e;else if(e.zone.type==="fixed"){let o=-1*(e.offset/60),a=o>=0?`Etc/GMT+${o}`:`Etc/GMT${o}`;e.offset!==0&&A.create(a).valid?(i=a,this.dt=e):(i="UTC",this.dt=e.offset===0?e:e.setZone("UTC").plus({minutes:e.offset}),this.originalZone=e.zone)}else e.zone.type==="system"?this.dt=e:e.zone.type==="iana"?(this.dt=e,i=e.zone.name):(i="UTC",this.dt=e.setZone("UTC").plus({minutes:e.offset}),this.originalZone=e.zone);let s=l({},this.opts);s.timeZone=s.timeZone||i,this.dtf=Ln(t,s)}format(){return this.originalZone?this.formatToParts().map(({value:e})=>e).join(""):this.dtf.format(this.dt.toJSDate())}formatToParts(){let e=this.dtf.formatToParts(this.dt.toJSDate());return this.originalZone?e.map(t=>{if(t.type==="timeZoneName"){let r=this.originalZone.offsetName(this.dt.ts,{locale:this.dt.locale,format:this.opts.timeZoneName});return b(l({},t),{value:r})}else return t}):e}resolvedOptions(){return this.dtf.resolvedOptions()}},Vn=class{constructor(e,t,r){this.opts=l({style:"long"},r),!t&&Vt()&&(this.rtf=_s(e,r))}format(e,t){return this.rtf?this.rtf.format(e,t):Gr(t,e,this.opts.numeric,this.opts.style!=="long")}formatToParts(e,t){return this.rtf?this.rtf.formatToParts(e,t):[]}},Zr={firstDay:1,minimalDays:4,weekend:[6,7]},T=class n{static fromOpts(e){return n.create(e.locale,e.numberingSystem,e.outputCalendar,e.weekSettings,e.defaultToEN)}static create(e,t,r,i,s=!1){let o=e||y.defaultLocale,a=o||(s?"en-US":js()),u=t||y.defaultNumberingSystem,c=r||y.defaultOutputCalendar,d=ct(i)||y.defaultWeekSettings;return new n(a,u,c,d,o)}static resetCache(){ut=null,xn.clear(),An.clear(),Rn.clear(),$n.clear(),zn.clear()}static fromObject({locale:e,numberingSystem:t,outputCalendar:r,weekSettings:i}={}){return n.create(e,t,r,i)}constructor(e,t,r,i,s){let[o,a,u]=Us(e);this.locale=o,this.numberingSystem=t||a||null,this.outputCalendar=r||u||null,this.weekSettings=i,this.intl=Ws(this.locale,this.numberingSystem,this.outputCalendar),this.weekdaysCache={format:{},standalone:{}},this.monthsCache={format:{},standalone:{}},this.meridiemCache=null,this.eraCache={},this.specifiedLocale=s,this.fastNumbersCached=null}get fastNumbers(){return this.fastNumbersCached==null&&(this.fastNumbersCached=Zs(this)),this.fastNumbersCached}listingMode(){let e=this.isEnglish(),t=(this.numberingSystem===null||this.numberingSystem==="latn")&&(this.outputCalendar===null||this.outputCalendar==="gregory");return e&&t?"en":"intl"}clone(e){return!e||Object.getOwnPropertyNames(e).length===0?this:n.create(e.locale||this.specifiedLocale,e.numberingSystem||this.numberingSystem,e.outputCalendar||this.outputCalendar,ct(e.weekSettings)||this.weekSettings,e.defaultToEN||!1)}redefaultToEN(e={}){return this.clone(b(l({},e),{defaultToEN:!0}))}redefaultToSystem(e={}){return this.clone(b(l({},e),{defaultToEN:!1}))}months(e,t=!1){return jt(this,e,Un,()=>{let r=this.intl==="ja"||this.intl.startsWith("ja-");t&=!r;let i=t?{month:e,day:"numeric"}:{month:e},s=t?"format":"standalone";if(!this.monthsCache[s][e]){let o=r?a=>this.dtFormatter(a,i).format():a=>this.extract(a,i,"month");this.monthsCache[s][e]=Ps(o)}return this.monthsCache[s][e]})}weekdays(e,t=!1){return jt(this,e,Wn,()=>{let r=t?{weekday:e,year:"numeric",month:"long",day:"numeric"}:{weekday:e},i=t?"format":"standalone";return this.weekdaysCache[i][e]||(this.weekdaysCache[i][e]=Hs(s=>this.extract(s,r,"weekday"))),this.weekdaysCache[i][e]})}meridiems(){return jt(this,void 0,()=>Pn,()=>{if(!this.meridiemCache){let e={hour:"numeric",hourCycle:"h12"};this.meridiemCache=[v.utc(2016,11,13,9),v.utc(2016,11,13,19)].map(t=>this.extract(t,e,"dayperiod"))}return this.meridiemCache})}eras(e){return jt(this,e,Hn,()=>{let t={era:e};return this.eraCache[e]||(this.eraCache[e]=[v.utc(-40,1,1),v.utc(2017,1,1)].map(r=>this.extract(r,t,"era"))),this.eraCache[e]})}extract(e,t,r){let i=this.dtFormatter(e,t),s=i.formatToParts(),o=s.find(a=>a.type.toLowerCase()===r);return o?o.value:null}numberFormatter(e={}){return new _n(this.intl,e.forceSimple||this.fastNumbers,e)}dtFormatter(e,t={}){return new jn(e,this.intl,t)}relFormatter(e={}){return new Vn(this.intl,this.isEnglish(),e)}listFormatter(e={}){return $s(this.intl,e)}isEnglish(){return this.locale==="en"||this.locale.toLowerCase()==="en-us"||Hr(this.intl).locale.startsWith("en-us")}getWeekSettings(){return this.weekSettings?this.weekSettings:Ut()?Vs(this.locale):Zr}getStartOfWeek(){return this.getWeekSettings().firstDay}getMinDaysInFirstWeek(){return this.getWeekSettings().minimalDays}getWeekendDays(){return this.getWeekSettings().weekend}equals(e){return this.locale===e.locale&&this.numberingSystem===e.numberingSystem&&this.outputCalendar===e.outputCalendar}toString(){return`Locale(${this.locale}, ${this.numberingSystem}, ${this.outputCalendar})`}};var Gn=null,F=class n extends ${static get utcInstance(){return Gn===null&&(Gn=new n(0)),Gn}static instance(e){return e===0?n.utcInstance:new n(e)}static parseSpecifier(e){if(e){let t=e.match(/^utc(?:([+-]\d{1,2})(?::(\d{2}))?)?$/i);if(t)return new n(de(t[1],t[2]))}return null}constructor(e){super(),this.fixed=e}get type(){return"fixed"}get name(){return this.fixed===0?"UTC":`UTC${ne(this.fixed,"narrow")}`}get ianaName(){return this.fixed===0?"Etc/UTC":`Etc/GMT${ne(-this.fixed,"narrow")}`}offsetName(){return this.name}formatOffset(e,t){return ne(this.fixed,t)}get isUniversal(){return!0}offset(){return this.fixed}equals(e){return e.type==="fixed"&&e.fixed===this.fixed}get isValid(){return!0}};var lt=class extends ${constructor(e){super(),this.zoneName=e}get type(){return"invalid"}get name(){return this.zoneName}get isUniversal(){return!1}offsetName(){return null}formatOffset(){return""}offset(){return NaN}equals(){return!1}get isValid(){return!1}};function Z(n,e){let t;if(p(n)||n===null)return e;if(n instanceof $)return n;if(qr(n)){let r=n.toLowerCase();return r==="default"?e:r==="local"||r==="system"?le.instance:r==="utc"||r==="gmt"?F.utcInstance:F.parseSpecifier(r)||A.create(n)}else return G(n)?F.instance(n):typeof n=="object"&&"offset"in n&&typeof n.offset=="function"?n:new lt(n)}var Yn={arab:"[\u0660-\u0669]",arabext:"[\u06F0-\u06F9]",bali:"[\u1B50-\u1B59]",beng:"[\u09E6-\u09EF]",deva:"[\u0966-\u096F]",fullwide:"[\uFF10-\uFF19]",gujr:"[\u0AE6-\u0AEF]",hanidec:"[\u3007|\u4E00|\u4E8C|\u4E09|\u56DB|\u4E94|\u516D|\u4E03|\u516B|\u4E5D]",khmr:"[\u17E0-\u17E9]",knda:"[\u0CE6-\u0CEF]",laoo:"[\u0ED0-\u0ED9]",limb:"[\u1946-\u194F]",mlym:"[\u0D66-\u0D6F]",mong:"[\u1810-\u1819]",mymr:"[\u1040-\u1049]",orya:"[\u0B66-\u0B6F]",tamldec:"[\u0BE6-\u0BEF]",telu:"[\u0C66-\u0C6F]",thai:"[\u0E50-\u0E59]",tibt:"[\u0F20-\u0F29]",latn:"\\d"},Yr={arab:[1632,1641],arabext:[1776,1785],bali:[6992,7001],beng:[2534,2543],deva:[2406,2415],fullwide:[65296,65303],gujr:[2790,2799],khmr:[6112,6121],knda:[3302,3311],laoo:[3792,3801],limb:[6470,6479],mlym:[3430,3439],mong:[6160,6169],mymr:[4160,4169],orya:[2918,2927],tamldec:[3046,3055],telu:[3174,3183],thai:[3664,3673],tibt:[3872,3881]},Gs=Yn.hanidec.replace(/[\[|\]]/g,"").split("");function Br(n){let e=parseInt(n,10);if(isNaN(e)){e="";for(let t=0;t<n.length;t++){let r=n.charCodeAt(t);if(n[t].search(Yn.hanidec)!==-1)e+=Gs.indexOf(n[t]);else for(let i in Yr){let[s,o]=Yr[i];r>=s&&r<=o&&(e+=r-s)}}return parseInt(e,10)}else return e}var qn=new Map;function Kr(){qn.clear()}function _({numberingSystem:n},e=""){let t=n||"latn",r=qn.get(t);r===void 0&&(r=new Map,qn.set(t,r));let i=r.get(e);return i===void 0&&(i=new RegExp(`${Yn[t]}${e}`),r.set(e,i)),i}var Jr=()=>Date.now(),Qr="system",Xr=null,ei=null,ti=null,ni=60,ri,ii=null,y=class{static get now(){return Jr}static set now(e){Jr=e}static set defaultZone(e){Qr=e}static get defaultZone(){return Z(Qr,le.instance)}static get defaultLocale(){return Xr}static set defaultLocale(e){Xr=e}static get defaultNumberingSystem(){return ei}static set defaultNumberingSystem(e){ei=e}static get defaultOutputCalendar(){return ti}static set defaultOutputCalendar(e){ti=e}static get defaultWeekSettings(){return ii}static set defaultWeekSettings(e){ii=ct(e)}static get twoDigitCutoffYear(){return ni}static set twoDigitCutoffYear(e){ni=e%100}static get throwOnInvalid(){return ri}static set throwOnInvalid(e){ri=e}static resetCaches(){T.resetCache(),A.resetCache(),v.resetCache(),Kr()}};var x=class{constructor(e,t){this.reason=e,this.explanation=t}toMessage(){return this.explanation?`${this.reason}: ${this.explanation}`:this.reason}};var si=[0,31,59,90,120,151,181,212,243,273,304,334],oi=[0,31,60,91,121,152,182,213,244,274,305,335];function j(n,e){return new x("unit out of range",`you specified ${e} (of type ${typeof e}) as a ${n}, which is invalid`)}function Wt(n,e,t){let r=new Date(Date.UTC(n,e-1,t));n<100&&n>=0&&r.setUTCFullYear(r.getUTCFullYear()-1900);let i=r.getUTCDay();return i===0?7:i}function ai(n,e,t){return t+(he(n)?oi:si)[e-1]}function ui(n,e){let t=he(n)?oi:si,r=t.findIndex(s=>s<e),i=e-t[r];return{month:r+1,day:i}}function Pt(n,e){return(n-e+7)%7+1}function dt(n,e=4,t=1){let{year:r,month:i,day:s}=n,o=ai(r,i,s),a=Pt(Wt(r,i,s),t),u=Math.floor((o-a+14-e)/7),c;return u<1?(c=r-1,u=fe(c,e,t)):u>fe(r,e,t)?(c=r+1,u=1):c=r,l({weekYear:c,weekNumber:u,weekday:a},ht(n))}function Bn(n,e=4,t=1){let{weekYear:r,weekNumber:i,weekday:s}=n,o=Pt(Wt(r,1,e),t),a=re(r),u=i*7+s-o-7+e,c;u<1?(c=r-1,u+=re(c)):u>a?(c=r+1,u-=re(r)):c=r;let{month:d,day:h}=ui(c,u);return l({year:c,month:d,day:h},ht(n))}function Ht(n){let{year:e,month:t,day:r}=n,i=ai(e,t,r);return l({year:e,ordinal:i},ht(n))}function Kn(n){let{year:e,ordinal:t}=n,{month:r,day:i}=ui(e,t);return l({year:e,month:r,day:i},ht(n))}function Jn(n,e){if(!p(n.localWeekday)||!p(n.localWeekNumber)||!p(n.localWeekYear)){if(!p(n.weekday)||!p(n.weekNumber)||!p(n.weekYear))throw new W("Cannot mix locale-based week fields with ISO-based week fields");return p(n.localWeekday)||(n.weekday=n.localWeekday),p(n.localWeekNumber)||(n.weekNumber=n.localWeekNumber),p(n.localWeekYear)||(n.weekYear=n.localWeekYear),delete n.localWeekday,delete n.localWeekNumber,delete n.localWeekYear,{minDaysInFirstWeek:e.getMinDaysInFirstWeek(),startOfWeek:e.getStartOfWeek()}}else return{minDaysInFirstWeek:4,startOfWeek:1}}function ci(n,e=4,t=1){let r=ft(n.weekYear),i=z(n.weekNumber,1,fe(n.weekYear,e,t)),s=z(n.weekday,1,7);return r?i?s?!1:j("weekday",n.weekday):j("week",n.weekNumber):j("weekYear",n.weekYear)}function li(n){let e=ft(n.year),t=z(n.ordinal,1,re(n.year));return e?t?!1:j("ordinal",n.ordinal):j("year",n.year)}function Qn(n){let e=ft(n.year),t=z(n.month,1,12),r=z(n.day,1,be(n.year,n.month));return e?t?r?!1:j("day",n.day):j("month",n.month):j("year",n.year)}function Xn(n){let{hour:e,minute:t,second:r,millisecond:i}=n,s=z(e,0,23)||e===24&&t===0&&r===0&&i===0,o=z(t,0,59),a=z(r,0,59),u=z(i,0,999);return s?o?a?u?!1:j("millisecond",i):j("second",r):j("minute",t):j("hour",e)}function p(n){return typeof n>"u"}function G(n){return typeof n=="number"}function ft(n){return typeof n=="number"&&n%1===0}function qr(n){return typeof n=="string"}function fi(n){return Object.prototype.toString.call(n)==="[object Date]"}function Vt(){try{return typeof Intl<"u"&&!!Intl.RelativeTimeFormat}catch{return!1}}function Ut(){try{return typeof Intl<"u"&&!!Intl.Locale&&("weekInfo"in Intl.Locale.prototype||"getWeekInfo"in Intl.Locale.prototype)}catch{return!1}}function hi(n){return Array.isArray(n)?n:[n]}function er(n,e,t){if(n.length!==0)return n.reduce((r,i)=>{let s=[e(i),i];return r&&t(r[0],s[0])===r[0]?r:s},null)[1]}function mi(n,e){return e.reduce((t,r)=>(t[r]=n[r],t),{})}function ie(n,e){return Object.prototype.hasOwnProperty.call(n,e)}function ct(n){if(n==null)return null;if(typeof n!="object")throw new k("Week settings must be an object");if(!z(n.firstDay,1,7)||!z(n.minimalDays,1,7)||!Array.isArray(n.weekend)||n.weekend.some(e=>!z(e,1,7)))throw new k("Invalid week settings");return{firstDay:n.firstDay,minimalDays:n.minimalDays,weekend:Array.from(n.weekend)}}function z(n,e,t){return ft(n)&&n>=e&&n<=t}function qs(n,e){return n-e*Math.floor(n/e)}function O(n,e=2){let t=n<0,r;return t?r="-"+(""+-n).padStart(e,"0"):r=(""+n).padStart(e,"0"),r}function Q(n){if(!(p(n)||n===null||n===""))return parseInt(n,10)}function se(n){if(!(p(n)||n===null||n===""))return parseFloat(n)}function mt(n){if(!(p(n)||n===null||n==="")){let e=parseFloat("0."+n)*1e3;return Math.floor(e)}}function Ee(n,e,t="round"){let r=10**e;switch(t){case"expand":return n>0?Math.ceil(n*r)/r:Math.floor(n*r)/r;case"trunc":return Math.trunc(n*r)/r;case"round":return Math.round(n*r)/r;case"floor":return Math.floor(n*r)/r;case"ceil":return Math.ceil(n*r)/r;default:throw new RangeError(`Value rounding ${t} is out of range`)}}function he(n){return n%4===0&&(n%100!==0||n%400===0)}function re(n){return he(n)?366:365}function be(n,e){let t=qs(e-1,12)+1,r=n+(e-t)/12;return t===2?he(r)?29:28:[31,null,31,30,31,30,31,31,30,31,30,31][t-1]}function Se(n){let e=Date.UTC(n.year,n.month-1,n.day,n.hour,n.minute,n.second,n.millisecond);return n.year<100&&n.year>=0&&(e=new Date(e),e.setUTCFullYear(n.year,n.month-1,n.day)),+e}function di(n,e,t){return-Pt(Wt(n,1,e),t)+e-1}function fe(n,e=4,t=1){let r=di(n,e,t),i=di(n+1,e,t);return(re(n)-r+i)/7}function pt(n){return n>99?n:n>y.twoDigitCutoffYear?1900+n:2e3+n}function _t(n,e,t,r=null){let i=new Date(n),s={hourCycle:"h23",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"};r&&(s.timeZone=r);let o=l({timeZoneName:e},s),a=new Intl.DateTimeFormat(t,o).formatToParts(i).find(u=>u.type.toLowerCase()==="timezonename");return a?a.value:null}function de(n,e){let t=parseInt(n,10);Number.isNaN(t)&&(t=0);let r=parseInt(e,10)||0,i=t<0||Object.is(t,-0)?-r:r;return t*60+i}function tr(n){let e=Number(n);if(typeof n=="boolean"||n===""||!Number.isFinite(e))throw new k(`Invalid unit value ${n}`);return e}function Ie(n,e){let t={};for(let r in n)if(ie(n,r)){let i=n[r];if(i==null)continue;t[e(r)]=tr(i)}return t}function ne(n,e){let t=Math.trunc(Math.abs(n/60)),r=Math.trunc(Math.abs(n%60)),i=n>=0?"+":"-";switch(e){case"short":return`${i}${O(t,2)}:${O(r,2)}`;case"narrow":return`${i}${t}${r>0?`:${r}`:""}`;case"techie":return`${i}${O(t,2)}${O(r,2)}`;default:throw new RangeError(`Value format ${e} is out of range for property format`)}}function ht(n){return mi(n,["hour","minute","second","millisecond"])}var Ys=["January","February","March","April","May","June","July","August","September","October","November","December"],nr=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],Bs=["J","F","M","A","M","J","J","A","S","O","N","D"];function Un(n){switch(n){case"narrow":return[...Bs];case"short":return[...nr];case"long":return[...Ys];case"numeric":return["1","2","3","4","5","6","7","8","9","10","11","12"];case"2-digit":return["01","02","03","04","05","06","07","08","09","10","11","12"];default:return null}}var rr=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],ir=["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],Ks=["M","T","W","T","F","S","S"];function Wn(n){switch(n){case"narrow":return[...Ks];case"short":return[...ir];case"long":return[...rr];case"numeric":return["1","2","3","4","5","6","7"];default:return null}}var Pn=["AM","PM"],Js=["Before Christ","Anno Domini"],Qs=["BC","AD"],Xs=["B","A"];function Hn(n){switch(n){case"narrow":return[...Xs];case"short":return[...Qs];case"long":return[...Js];default:return null}}function pi(n){return Pn[n.hour<12?0:1]}function gi(n,e){return Wn(e)[n.weekday-1]}function yi(n,e){return Un(e)[n.month-1]}function vi(n,e){return Hn(e)[n.year<0?0:1]}function Gr(n,e,t="always",r=!1){let i={years:["year","yr."],quarters:["quarter","qtr."],months:["month","mo."],weeks:["week","wk."],days:["day","day","days"],hours:["hour","hr."],minutes:["minute","min."],seconds:["second","sec."]},s=["hours","minutes","seconds"].indexOf(n)===-1;if(t==="auto"&&s){let h=n==="days";switch(e){case 1:return h?"tomorrow":`next ${i[n][0]}`;case-1:return h?"yesterday":`last ${i[n][0]}`;case 0:return h?"today":`this ${i[n][0]}`;default:}}let o=Object.is(e,-0)||e<0,a=Math.abs(e),u=a===1,c=i[n],d=r?u?c[1]:c[2]||c[1]:u?i[n][0]:n;return o?`${a} ${d} ago`:`in ${a} ${d}`}function Ti(n,e){let t="";for(let r of n)r.literal?t+=r.val:t+=e(r.val);return t}var eo={D:te,DD:Pe,DDD:He,DDDD:Ze,t:Ge,tt:qe,ttt:Ye,tttt:Be,T:Ke,TT:Je,TTT:Qe,TTTT:Xe,f:et,ff:nt,fff:it,ffff:ot,F:tt,FF:rt,FFF:st,FFFF:at},L=class n{static create(e,t={}){return new n(e,t)}static parseFormat(e){let t=null,r="",i=!1,s=[];for(let o=0;o<e.length;o++){let a=e.charAt(o);a==="'"?((r.length>0||i)&&s.push({literal:i||/^\s+$/.test(r),val:r===""?"'":r}),t=null,r="",i=!i):i||a===t?r+=a:(r.length>0&&s.push({literal:/^\s+$/.test(r),val:r}),r=a,t=a)}return r.length>0&&s.push({literal:i||/^\s+$/.test(r),val:r}),s}static macroTokenToFormatOpts(e){return eo[e]}constructor(e,t){this.opts=t,this.loc=e,this.systemLoc=null}formatWithSystemDefault(e,t){return this.systemLoc===null&&(this.systemLoc=this.loc.redefaultToSystem()),this.systemLoc.dtFormatter(e,l(l({},this.opts),t)).format()}dtFormatter(e,t={}){return this.loc.dtFormatter(e,l(l({},this.opts),t))}formatDateTime(e,t){return this.dtFormatter(e,t).format()}formatDateTimeParts(e,t){return this.dtFormatter(e,t).formatToParts()}formatInterval(e,t){return this.dtFormatter(e.start,t).dtf.formatRange(e.start.toJSDate(),e.end.toJSDate())}resolvedOptions(e,t){return this.dtFormatter(e,t).resolvedOptions()}num(e,t=0,r=void 0){if(this.opts.forceSimple)return O(e,t);let i=l({},this.opts);return t>0&&(i.padTo=t),r&&(i.signDisplay=r),this.loc.numberFormatter(i).format(e)}formatDateTimeFromString(e,t){let r=this.loc.listingMode()==="en",i=this.loc.outputCalendar&&this.loc.outputCalendar!=="gregory",s=(m,M)=>this.loc.extract(e,m,M),o=m=>e.isOffsetFixed&&e.offset===0&&m.allowZ?"Z":e.isValid?e.zone.formatOffset(e.ts,m.format):"",a=()=>r?pi(e):s({hour:"numeric",hourCycle:"h12"},"dayperiod"),u=(m,M)=>r?yi(e,m):s(M?{month:m}:{month:m,day:"numeric"},"month"),c=(m,M)=>r?gi(e,m):s(M?{weekday:m}:{weekday:m,month:"long",day:"numeric"},"weekday"),d=m=>{let M=n.macroTokenToFormatOpts(m);return M?this.formatWithSystemDefault(e,M):m},h=m=>r?vi(e,m):s({era:m},"era"),g=m=>{switch(m){case"S":return this.num(e.millisecond);case"u":case"SSS":return this.num(e.millisecond,3);case"s":return this.num(e.second);case"ss":return this.num(e.second,2);case"uu":return this.num(Math.floor(e.millisecond/10),2);case"uuu":return this.num(Math.floor(e.millisecond/100));case"m":return this.num(e.minute);case"mm":return this.num(e.minute,2);case"h":return this.num(e.hour%12===0?12:e.hour%12);case"hh":return this.num(e.hour%12===0?12:e.hour%12,2);case"H":return this.num(e.hour);case"HH":return this.num(e.hour,2);case"Z":return o({format:"narrow",allowZ:this.opts.allowZ});case"ZZ":return o({format:"short",allowZ:this.opts.allowZ});case"ZZZ":return o({format:"techie",allowZ:this.opts.allowZ});case"ZZZZ":return e.zone.offsetName(e.ts,{format:"short",locale:this.loc.locale});case"ZZZZZ":return e.zone.offsetName(e.ts,{format:"long",locale:this.loc.locale});case"z":return e.zoneName;case"a":return a();case"d":return i?s({day:"numeric"},"day"):this.num(e.day);case"dd":return i?s({day:"2-digit"},"day"):this.num(e.day,2);case"c":return this.num(e.weekday);case"ccc":return c("short",!0);case"cccc":return c("long",!0);case"ccccc":return c("narrow",!0);case"E":return this.num(e.weekday);case"EEE":return c("short",!1);case"EEEE":return c("long",!1);case"EEEEE":return c("narrow",!1);case"L":return i?s({month:"numeric",day:"numeric"},"month"):this.num(e.month);case"LL":return i?s({month:"2-digit",day:"numeric"},"month"):this.num(e.month,2);case"LLL":return u("short",!0);case"LLLL":return u("long",!0);case"LLLLL":return u("narrow",!0);case"M":return i?s({month:"numeric"},"month"):this.num(e.month);case"MM":return i?s({month:"2-digit"},"month"):this.num(e.month,2);case"MMM":return u("short",!1);case"MMMM":return u("long",!1);case"MMMMM":return u("narrow",!1);case"y":return i?s({year:"numeric"},"year"):this.num(e.year);case"yy":return i?s({year:"2-digit"},"year"):this.num(e.year.toString().slice(-2),2);case"yyyy":return i?s({year:"numeric"},"year"):this.num(e.year,4);case"yyyyyy":return i?s({year:"numeric"},"year"):this.num(e.year,6);case"G":return h("short");case"GG":return h("long");case"GGGGG":return h("narrow");case"kk":return this.num(e.weekYear.toString().slice(-2),2);case"kkkk":return this.num(e.weekYear,4);case"W":return this.num(e.weekNumber);case"WW":return this.num(e.weekNumber,2);case"n":return this.num(e.localWeekNumber);case"nn":return this.num(e.localWeekNumber,2);case"ii":return this.num(e.localWeekYear.toString().slice(-2),2);case"iiii":return this.num(e.localWeekYear,4);case"o":return this.num(e.ordinal);case"ooo":return this.num(e.ordinal,3);case"q":return this.num(e.quarter);case"qq":return this.num(e.quarter,2);case"X":return this.num(Math.floor(e.ts/1e3));case"x":return this.num(e.ts);default:return d(m)}};return Ti(n.parseFormat(t),g)}formatDurationFromString(e,t){let r=this.opts.signMode==="negativeLargestOnly"?-1:1,i=d=>{switch(d[0]){case"S":return"milliseconds";case"s":return"seconds";case"m":return"minutes";case"h":return"hours";case"d":return"days";case"w":return"weeks";case"M":return"months";case"y":return"years";default:return null}},s=(d,h)=>g=>{let m=i(g);if(m){let M=h.isNegativeDuration&&m!==h.largestUnit?r:1,U;return this.opts.signMode==="negativeLargestOnly"&&m!==h.largestUnit?U="never":this.opts.signMode==="all"?U="always":U="auto",this.num(d.get(m)*M,g.length,U)}else return g},o=n.parseFormat(t),a=o.reduce((d,{literal:h,val:g})=>h?d:d.concat(g),[]),u=e.shiftTo(...a.map(i).filter(d=>d)),c={isNegativeDuration:u<0,largestUnit:Object.keys(u.values)[0]};return Ti(o,s(u,c))}};var Si=/[A-Za-z_+-]{1,256}(?::?\/[A-Za-z0-9_+-]{1,256}(?:\/[A-Za-z0-9_+-]{1,256})?)?/;function ke(...n){let e=n.reduce((t,r)=>t+r.source,"");return RegExp(`^${e}$`)}function De(...n){return e=>n.reduce(([t,r,i],s)=>{let[o,a,u]=s(e,i);return[l(l({},t),o),a||r,u]},[{},null,1]).slice(0,2)}function Me(n,...e){if(n==null)return[null,null];for(let[t,r]of e){let i=t.exec(n);if(i)return r(i)}return[null,null]}function Ei(...n){return(e,t)=>{let r={},i;for(i=0;i<n.length;i++)r[n[i]]=Q(e[t+i]);return[r,null,t+i]}}var bi=/(?:([Zz])|([+-]\d\d)(?::?(\d\d))?)/,to=`(?:${bi.source}?(?:\\[(${Si.source})\\])?)?`,sr=/(\d\d)(?::?(\d\d)(?::?(\d\d)(?:[.,](\d{1,30}))?)?)?/,Ii=RegExp(`${sr.source}${to}`),or=RegExp(`(?:[Tt]${Ii.source})?`),no=/([+-]\d{6}|\d{4})(?:-?(\d\d)(?:-?(\d\d))?)?/,ro=/(\d{4})-?W(\d\d)(?:-?(\d))?/,io=/(\d{4})-?(\d{3})/,so=Ei("weekYear","weekNumber","weekDay"),oo=Ei("year","ordinal"),ao=/(\d{4})-(\d\d)-(\d\d)/,Oi=RegExp(`${sr.source} ?(?:${bi.source}|(${Si.source}))?`),uo=RegExp(`(?: ${Oi.source})?`);function Oe(n,e,t){let r=n[e];return p(r)?t:Q(r)}function co(n,e){return[{year:Oe(n,e),month:Oe(n,e+1,1),day:Oe(n,e+2,1)},null,e+3]}function Ce(n,e){return[{hours:Oe(n,e,0),minutes:Oe(n,e+1,0),seconds:Oe(n,e+2,0),milliseconds:mt(n[e+3])},null,e+4]}function gt(n,e){let t=!n[e]&&!n[e+1],r=de(n[e+1],n[e+2]),i=t?null:F.instance(r);return[{},i,e+3]}function yt(n,e){let t=n[e]?A.create(n[e]):null;return[{},t,e+1]}var lo=RegExp(`^T?${sr.source}$`),fo=/^-?P(?:(?:(-?\d{1,20}(?:\.\d{1,20})?)Y)?(?:(-?\d{1,20}(?:\.\d{1,20})?)M)?(?:(-?\d{1,20}(?:\.\d{1,20})?)W)?(?:(-?\d{1,20}(?:\.\d{1,20})?)D)?(?:T(?:(-?\d{1,20}(?:\.\d{1,20})?)H)?(?:(-?\d{1,20}(?:\.\d{1,20})?)M)?(?:(-?\d{1,20})(?:[.,](-?\d{1,20}))?S)?)?)$/;function ho(n){let[e,t,r,i,s,o,a,u,c]=n,d=e[0]==="-",h=u&&u[0]==="-",g=(m,M=!1)=>m!==void 0&&(M||m&&d)?-m:m;return[{years:g(se(t)),months:g(se(r)),weeks:g(se(i)),days:g(se(s)),hours:g(se(o)),minutes:g(se(a)),seconds:g(se(u),u==="-0"),milliseconds:g(mt(c),h)}]}var mo={GMT:0,EDT:-4*60,EST:-5*60,CDT:-5*60,CST:-6*60,MDT:-6*60,MST:-7*60,PDT:-7*60,PST:-8*60};function ar(n,e,t,r,i,s,o){let a={year:e.length===2?pt(Q(e)):Q(e),month:nr.indexOf(t)+1,day:Q(r),hour:Q(i),minute:Q(s)};return o&&(a.second=Q(o)),n&&(a.weekday=n.length>3?rr.indexOf(n)+1:ir.indexOf(n)+1),a}var po=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|(?:([+-]\d\d)(\d\d)))$/;function go(n){let[,e,t,r,i,s,o,a,u,c,d,h]=n,g=ar(e,i,r,t,s,o,a),m;return u?m=mo[u]:c?m=0:m=de(d,h),[g,new F(m)]}function yo(n){return n.replace(/\([^()]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").trim()}var vo=/^(Mon|Tue|Wed|Thu|Fri|Sat|Sun), (\d\d) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) (\d{4}) (\d\d):(\d\d):(\d\d) GMT$/,To=/^(Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday), (\d\d)-(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-(\d\d) (\d\d):(\d\d):(\d\d) GMT$/,wo=/^(Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) ( \d|\d\d) (\d\d):(\d\d):(\d\d) (\d{4})$/;function wi(n){let[,e,t,r,i,s,o,a]=n;return[ar(e,i,r,t,s,o,a),F.utcInstance]}function So(n){let[,e,t,r,i,s,o,a]=n;return[ar(e,a,t,r,i,s,o),F.utcInstance]}var Eo=ke(no,or),bo=ke(ro,or),Io=ke(io,or),Oo=ke(Ii),ki=De(co,Ce,gt,yt),ko=De(so,Ce,gt,yt),Do=De(oo,Ce,gt,yt),Mo=De(Ce,gt,yt);function Di(n){return Me(n,[Eo,ki],[bo,ko],[Io,Do],[Oo,Mo])}function Mi(n){return Me(yo(n),[po,go])}function Ci(n){return Me(n,[vo,wi],[To,wi],[wo,So])}function Ni(n){return Me(n,[fo,ho])}var Co=De(Ce);function Fi(n){return Me(n,[lo,Co])}var No=ke(ao,uo),Fo=ke(Oi),xo=De(Ce,gt,yt);function xi(n){return Me(n,[No,ki],[Fo,xo])}var Li="Invalid Duration",$i={weeks:{days:7,hours:7*24,minutes:7*24*60,seconds:7*24*60*60,milliseconds:7*24*60*60*1e3},days:{hours:24,minutes:24*60,seconds:24*60*60,milliseconds:24*60*60*1e3},hours:{minutes:60,seconds:60*60,milliseconds:60*60*1e3},minutes:{seconds:60,milliseconds:60*1e3},seconds:{milliseconds:1e3}},Lo=l({years:{quarters:4,months:12,weeks:52,days:365,hours:365*24,minutes:365*24*60,seconds:365*24*60*60,milliseconds:365*24*60*60*1e3},quarters:{months:3,weeks:13,days:91,hours:91*24,minutes:91*24*60,seconds:91*24*60*60,milliseconds:91*24*60*60*1e3},months:{weeks:4,days:30,hours:30*24,minutes:30*24*60,seconds:30*24*60*60,milliseconds:30*24*60*60*1e3}},$i),V=146097/400,Ne=146097/4800,Ao=l({years:{quarters:4,months:12,weeks:V/7,days:V,hours:V*24,minutes:V*24*60,seconds:V*24*60*60,milliseconds:V*24*60*60*1e3},quarters:{months:3,weeks:V/28,days:V/4,hours:V*24/4,minutes:V*24*60/4,seconds:V*24*60*60/4,milliseconds:V*24*60*60*1e3/4},months:{weeks:Ne/7,days:Ne,hours:Ne*24,minutes:Ne*24*60,seconds:Ne*24*60*60,milliseconds:Ne*24*60*60*1e3}},$i),me=["years","quarters","months","weeks","days","hours","minutes","seconds","milliseconds"],Ro=me.slice(0).reverse();function X(n,e,t=!1){let r={values:t?e.values:l(l({},n.values),e.values||{}),loc:n.loc.clone(e.loc),conversionAccuracy:e.conversionAccuracy||n.conversionAccuracy,matrix:e.matrix||n.matrix};return new D(r)}function zi(n,e){let t=e.milliseconds??0;for(let r of Ro.slice(1))e[r]&&(t+=e[r]*n[r].milliseconds);return t}function Ai(n,e){let t=zi(n,e)<0?-1:1;me.reduceRight((r,i)=>{if(p(e[i]))return r;if(r){let s=e[r]*t,o=n[i][r],a=Math.floor(s/o);e[i]+=a*t,e[r]-=a*o*t}return i},null),me.reduce((r,i)=>{if(p(e[i]))return r;if(r){let s=e[r]%1;e[r]-=s,e[i]+=s*n[r][i]}return i},null)}function Ri(n){let e={};for(let[t,r]of Object.entries(n))r!==0&&(e[t]=r);return e}var D=class n{constructor(e){let t=e.conversionAccuracy==="longterm"||!1,r=t?Ao:Lo;e.matrix&&(r=e.matrix),this.values=e.values,this.loc=e.loc||T.create(),this.conversionAccuracy=t?"longterm":"casual",this.invalid=e.invalid||null,this.matrix=r,this.isLuxonDuration=!0}static fromMillis(e,t){return n.fromObject({milliseconds:e},t)}static fromObject(e,t={}){if(e==null||typeof e!="object")throw new k(`Duration.fromObject: argument expected to be an object, got ${e===null?"null":typeof e}`);return new n({values:Ie(e,n.normalizeUnit),loc:T.fromObject(t),conversionAccuracy:t.conversionAccuracy,matrix:t.matrix})}static fromDurationLike(e){if(G(e))return n.fromMillis(e);if(n.isDuration(e))return e;if(typeof e=="object")return n.fromObject(e);throw new k(`Unknown duration argument ${e} of type ${typeof e}`)}static fromISO(e,t){let[r]=Ni(e);return r?n.fromObject(r,t):n.invalid("unparsable",`the input "${e}" can't be parsed as ISO 8601`)}static fromISOTime(e,t){let[r]=Fi(e);return r?n.fromObject(r,t):n.invalid("unparsable",`the input "${e}" can't be parsed as ISO 8601`)}static invalid(e,t=null){if(!e)throw new k("need to specify a reason the Duration is invalid");let r=e instanceof x?e:new x(e,t);if(y.throwOnInvalid)throw new $t(r);return new n({invalid:r})}static normalizeUnit(e){let t={year:"years",years:"years",quarter:"quarters",quarters:"quarters",month:"months",months:"months",week:"weeks",weeks:"weeks",day:"days",days:"days",hour:"hours",hours:"hours",minute:"minutes",minutes:"minutes",second:"seconds",seconds:"seconds",millisecond:"milliseconds",milliseconds:"milliseconds"}[e&&e.toLowerCase()];if(!t)throw new we(e);return t}static isDuration(e){return e&&e.isLuxonDuration||!1}get locale(){return this.isValid?this.loc.locale:null}get numberingSystem(){return this.isValid?this.loc.numberingSystem:null}toFormat(e,t={}){let r=b(l({},t),{floor:t.round!==!1&&t.floor!==!1});return this.isValid?L.create(this.loc,r).formatDurationFromString(this,e):Li}toHuman(e={}){if(!this.isValid)return Li;let t=e.showZeros!==!1,r=me.map(i=>{let s=this.values[i];return p(s)||s===0&&!t?null:this.loc.numberFormatter(b(l({style:"unit",unitDisplay:"long"},e),{unit:i.slice(0,-1)})).format(s)}).filter(i=>i);return this.loc.listFormatter(l({type:"conjunction",style:e.listStyle||"narrow"},e)).format(r)}toObject(){return this.isValid?l({},this.values):{}}toISO(){if(!this.isValid)return null;let e="P";return this.years!==0&&(e+=this.years+"Y"),(this.months!==0||this.quarters!==0)&&(e+=this.months+this.quarters*3+"M"),this.weeks!==0&&(e+=this.weeks+"W"),this.days!==0&&(e+=this.days+"D"),(this.hours!==0||this.minutes!==0||this.seconds!==0||this.milliseconds!==0)&&(e+="T"),this.hours!==0&&(e+=this.hours+"H"),this.minutes!==0&&(e+=this.minutes+"M"),(this.seconds!==0||this.milliseconds!==0)&&(e+=Ee(this.seconds+this.milliseconds/1e3,3)+"S"),e==="P"&&(e+="T0S"),e}toISOTime(e={}){if(!this.isValid)return null;let t=this.toMillis();return t<0||t>=864e5?null:(e=b(l({suppressMilliseconds:!1,suppressSeconds:!1,includePrefix:!1,format:"extended"},e),{includeOffset:!1}),v.fromMillis(t,{zone:"UTC"}).toISOTime(e))}toJSON(){return this.toISO()}toString(){return this.toISO()}[Symbol.for("nodejs.util.inspect.custom")](){return this.isValid?`Duration { values: ${JSON.stringify(this.values)} }`:`Duration { Invalid, reason: ${this.invalidReason} }`}toMillis(){return this.isValid?zi(this.matrix,this.values):NaN}valueOf(){return this.toMillis()}plus(e){if(!this.isValid)return this;let t=n.fromDurationLike(e),r={};for(let i of me)(ie(t.values,i)||ie(this.values,i))&&(r[i]=t.get(i)+this.get(i));return X(this,{values:r},!0)}minus(e){if(!this.isValid)return this;let t=n.fromDurationLike(e);return this.plus(t.negate())}mapUnits(e){if(!this.isValid)return this;let t={};for(let r of Object.keys(this.values))t[r]=tr(e(this.values[r],r));return X(this,{values:t},!0)}get(e){return this[n.normalizeUnit(e)]}set(e){if(!this.isValid)return this;let t=l(l({},this.values),Ie(e,n.normalizeUnit));return X(this,{values:t})}reconfigure({locale:e,numberingSystem:t,conversionAccuracy:r,matrix:i}={}){let o={loc:this.loc.clone({locale:e,numberingSystem:t}),matrix:i,conversionAccuracy:r};return X(this,o)}as(e){return this.isValid?this.shiftTo(e).get(e):NaN}normalize(){if(!this.isValid)return this;let e=this.toObject();return Ai(this.matrix,e),X(this,{values:e},!0)}rescale(){if(!this.isValid)return this;let e=Ri(this.normalize().shiftToAll().toObject());return X(this,{values:e},!0)}shiftTo(...e){if(!this.isValid)return this;if(e.length===0)return this;e=e.map(o=>n.normalizeUnit(o));let t={},r={},i=this.toObject(),s;for(let o of me)if(e.indexOf(o)>=0){s=o;let a=0;for(let c in r)a+=this.matrix[c][o]*r[c],r[c]=0;G(i[o])&&(a+=i[o]);let u=Math.trunc(a);t[o]=u,r[o]=(a*1e3-u*1e3)/1e3}else G(i[o])&&(r[o]=i[o]);for(let o in r)r[o]!==0&&(t[s]+=o===s?r[o]:r[o]/this.matrix[s][o]);return Ai(this.matrix,t),X(this,{values:t},!0)}shiftToAll(){return this.isValid?this.shiftTo("years","months","weeks","days","hours","minutes","seconds","milliseconds"):this}negate(){if(!this.isValid)return this;let e={};for(let t of Object.keys(this.values))e[t]=this.values[t]===0?0:-this.values[t];return X(this,{values:e},!0)}removeZeros(){if(!this.isValid)return this;let e=Ri(this.values);return X(this,{values:e},!0)}get years(){return this.isValid?this.values.years||0:NaN}get quarters(){return this.isValid?this.values.quarters||0:NaN}get months(){return this.isValid?this.values.months||0:NaN}get weeks(){return this.isValid?this.values.weeks||0:NaN}get days(){return this.isValid?this.values.days||0:NaN}get hours(){return this.isValid?this.values.hours||0:NaN}get minutes(){return this.isValid?this.values.minutes||0:NaN}get seconds(){return this.isValid?this.values.seconds||0:NaN}get milliseconds(){return this.isValid?this.values.milliseconds||0:NaN}get isValid(){return this.invalid===null}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}equals(e){if(!this.isValid||!e.isValid||!this.loc.equals(e.loc))return!1;function t(r,i){return r===void 0||r===0?i===void 0||i===0:r===i}for(let r of me)if(!t(this.values[r],e.values[r]))return!1;return!0}};var Fe="Invalid Interval";function $o(n,e){return!n||!n.isValid?oe.invalid("missing or invalid start"):!e||!e.isValid?oe.invalid("missing or invalid end"):e<n?oe.invalid("end before start",`The end of an interval must be after its start, but you had start=${n.toISO()} and end=${e.toISO()}`):null}var oe=class n{constructor(e){this.s=e.start,this.e=e.end,this.invalid=e.invalid||null,this.isLuxonInterval=!0}static invalid(e,t=null){if(!e)throw new k("need to specify a reason the Interval is invalid");let r=e instanceof x?e:new x(e,t);if(y.throwOnInvalid)throw new Rt(r);return new n({invalid:r})}static fromDateTimes(e,t){let r=xe(e),i=xe(t),s=$o(r,i);return s??new n({start:r,end:i})}static after(e,t){let r=D.fromDurationLike(t),i=xe(e);return n.fromDateTimes(i,i.plus(r))}static before(e,t){let r=D.fromDurationLike(t),i=xe(e);return n.fromDateTimes(i.minus(r),i)}static fromISO(e,t){let[r,i]=(e||"").split("/",2);if(r&&i){let s,o;try{s=v.fromISO(r,t),o=s.isValid}catch{o=!1}let a,u;try{a=v.fromISO(i,t),u=a.isValid}catch{u=!1}if(o&&u)return n.fromDateTimes(s,a);if(o){let c=D.fromISO(i,t);if(c.isValid)return n.after(s,c)}else if(u){let c=D.fromISO(r,t);if(c.isValid)return n.before(a,c)}}return n.invalid("unparsable",`the input "${e}" can't be parsed as ISO 8601`)}static isInterval(e){return e&&e.isLuxonInterval||!1}get start(){return this.isValid?this.s:null}get end(){return this.isValid?this.e:null}get lastDateTime(){return this.isValid&&this.e?this.e.minus(1):null}get isValid(){return this.invalidReason===null}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}length(e="milliseconds"){return this.isValid?this.toDuration(e).get(e):NaN}count(e="milliseconds",t){if(!this.isValid)return NaN;let r=this.start.startOf(e,t),i;return t?.useLocaleWeeks?i=this.end.reconfigure({locale:r.locale}):i=this.end,i=i.startOf(e,t),Math.floor(i.diff(r,e).get(e))+(i.valueOf()!==this.end.valueOf())}hasSame(e){return this.isValid?this.isEmpty()||this.e.minus(1).hasSame(this.s,e):!1}isEmpty(){return this.s.valueOf()===this.e.valueOf()}isAfter(e){return this.isValid?this.s>e:!1}isBefore(e){return this.isValid?this.e<=e:!1}contains(e){return this.isValid?this.s<=e&&this.e>e:!1}set({start:e,end:t}={}){return this.isValid?n.fromDateTimes(e||this.s,t||this.e):this}splitAt(...e){if(!this.isValid)return[];let t=e.map(xe).filter(o=>this.contains(o)).sort((o,a)=>o.toMillis()-a.toMillis()),r=[],{s:i}=this,s=0;for(;i<this.e;){let o=t[s]||this.e,a=+o>+this.e?this.e:o;r.push(n.fromDateTimes(i,a)),i=a,s+=1}return r}splitBy(e){let t=D.fromDurationLike(e);if(!this.isValid||!t.isValid||t.as("milliseconds")===0)return[];let{s:r}=this,i=1,s,o=[];for(;r<this.e;){let a=this.start.plus(t.mapUnits(u=>u*i));s=+a>+this.e?this.e:a,o.push(n.fromDateTimes(r,s)),r=s,i+=1}return o}divideEqually(e){return this.isValid?this.splitBy(this.length()/e).slice(0,e):[]}overlaps(e){return this.e>e.s&&this.s<e.e}abutsStart(e){return this.isValid?+this.e==+e.s:!1}abutsEnd(e){return this.isValid?+e.e==+this.s:!1}engulfs(e){return this.isValid?this.s<=e.s&&this.e>=e.e:!1}equals(e){return!this.isValid||!e.isValid?!1:this.s.equals(e.s)&&this.e.equals(e.e)}intersection(e){if(!this.isValid)return this;let t=this.s>e.s?this.s:e.s,r=this.e<e.e?this.e:e.e;return t>=r?null:n.fromDateTimes(t,r)}union(e){if(!this.isValid)return this;let t=this.s<e.s?this.s:e.s,r=this.e>e.e?this.e:e.e;return n.fromDateTimes(t,r)}static merge(e){let[t,r]=e.sort((i,s)=>i.s-s.s).reduce(([i,s],o)=>s?s.overlaps(o)||s.abutsStart(o)?[i,s.union(o)]:[i.concat([s]),o]:[i,o],[[],null]);return r&&t.push(r),t}static xor(e){let t=null,r=0,i=[],s=e.map(u=>[{time:u.s,type:"s"},{time:u.e,type:"e"}]),o=Array.prototype.concat(...s),a=o.sort((u,c)=>u.time-c.time);for(let u of a)r+=u.type==="s"?1:-1,r===1?t=u.time:(t&&+t!=+u.time&&i.push(n.fromDateTimes(t,u.time)),t=null);return n.merge(i)}difference(...e){return n.xor([this].concat(e)).map(t=>this.intersection(t)).filter(t=>t&&!t.isEmpty())}toString(){return this.isValid?`[${this.s.toISO()} \u2013 ${this.e.toISO()})`:Fe}[Symbol.for("nodejs.util.inspect.custom")](){return this.isValid?`Interval { start: ${this.s.toISO()}, end: ${this.e.toISO()} }`:`Interval { Invalid, reason: ${this.invalidReason} }`}toLocaleString(e=te,t={}){return this.isValid?L.create(this.s.loc.clone(t),e).formatInterval(this):Fe}toISO(e){return this.isValid?`${this.s.toISO(e)}/${this.e.toISO(e)}`:Fe}toISODate(){return this.isValid?`${this.s.toISODate()}/${this.e.toISODate()}`:Fe}toISOTime(e){return this.isValid?`${this.s.toISOTime(e)}/${this.e.toISOTime(e)}`:Fe}toFormat(e,{separator:t=" \u2013 "}={}){return this.isValid?`${this.s.toFormat(e)}${t}${this.e.toFormat(e)}`:Fe}toDuration(e,t){return this.isValid?this.e.diff(this.s,e,t):D.invalid(this.invalidReason)}mapEndpoints(e){return n.fromDateTimes(e(this.s),e(this.e))}};var ae=class{static hasDST(e=y.defaultZone){let t=v.now().setZone(e).set({month:12});return!e.isUniversal&&t.offset!==t.set({month:6}).offset}static isValidIANAZone(e){return A.isValidZone(e)}static normalizeZone(e){return Z(e,y.defaultZone)}static getStartOfWeek({locale:e=null,locObj:t=null}={}){return(t||T.create(e)).getStartOfWeek()}static getMinimumDaysInFirstWeek({locale:e=null,locObj:t=null}={}){return(t||T.create(e)).getMinDaysInFirstWeek()}static getWeekendWeekdays({locale:e=null,locObj:t=null}={}){return(t||T.create(e)).getWeekendDays().slice()}static months(e="long",{locale:t=null,numberingSystem:r=null,locObj:i=null,outputCalendar:s="gregory"}={}){return(i||T.create(t,r,s)).months(e)}static monthsFormat(e="long",{locale:t=null,numberingSystem:r=null,locObj:i=null,outputCalendar:s="gregory"}={}){return(i||T.create(t,r,s)).months(e,!0)}static weekdays(e="long",{locale:t=null,numberingSystem:r=null,locObj:i=null}={}){return(i||T.create(t,r,null)).weekdays(e)}static weekdaysFormat(e="long",{locale:t=null,numberingSystem:r=null,locObj:i=null}={}){return(i||T.create(t,r,null)).weekdays(e,!0)}static meridiems({locale:e=null}={}){return T.create(e).meridiems()}static eras(e="short",{locale:t=null}={}){return T.create(t,null,"gregory").eras(e)}static features(){return{relative:Vt(),localeWeek:Ut()}}};function _i(n,e){let t=i=>i.toUTC(0,{keepLocalTime:!0}).startOf("day").valueOf(),r=t(e)-t(n);return Math.floor(D.fromMillis(r).as("days"))}function zo(n,e,t){let r=[["years",(u,c)=>c.year-u.year],["quarters",(u,c)=>c.quarter-u.quarter+(c.year-u.year)*4],["months",(u,c)=>c.month-u.month+(c.year-u.year)*12],["weeks",(u,c)=>{let d=_i(u,c);return(d-d%7)/7}],["days",_i]],i={},s=n,o,a;for(let[u,c]of r)t.indexOf(u)>=0&&(o=u,i[u]=c(n,e),a=s.plus(i),a>e?(i[u]--,n=s.plus(i),n>e&&(a=n,i[u]--,n=s.plus(i))):n=a);return[n,i,a,o]}function ji(n,e,t,r){let[i,s,o,a]=zo(n,e,t),u=e-i,c=t.filter(h=>["hours","minutes","seconds","milliseconds"].indexOf(h)>=0);c.length===0&&(o<e&&(o=i.plus({[a]:1})),o!==i&&(s[a]=(s[a]||0)+u/(o-i)));let d=D.fromObject(s,r);return c.length>0?D.fromMillis(u,r).shiftTo(...c).plus(d):d}var _o="missing Intl.DateTimeFormat.formatToParts support";function w(n,e=t=>t){return{regex:n,deser:([t])=>e(Br(t))}}var jo="\xA0",Wi=`[ ${jo}]`,Pi=new RegExp(Wi,"g");function Vo(n){return n.replace(/\./g,"\\.?").replace(Pi,Wi)}function Vi(n){return n.replace(/\./g,"").replace(Pi," ").toLowerCase()}function q(n,e){return n===null?null:{regex:RegExp(n.map(Vo).join("|")),deser:([t])=>n.findIndex(r=>Vi(t)===Vi(r))+e}}function Ui(n,e){return{regex:n,deser:([,t,r])=>de(t,r),groups:e}}function Zt(n){return{regex:n,deser:([e])=>e}}function Uo(n){return n.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&")}function Wo(n,e){let t=_(e),r=_(e,"{2}"),i=_(e,"{3}"),s=_(e,"{4}"),o=_(e,"{6}"),a=_(e,"{1,2}"),u=_(e,"{1,3}"),c=_(e,"{1,6}"),d=_(e,"{1,9}"),h=_(e,"{2,4}"),g=_(e,"{4,6}"),m=Y=>({regex:RegExp(Uo(Y.val)),deser:([ve])=>ve,literal:!0}),U=(Y=>{if(n.literal)return m(Y);switch(Y.val){case"G":return q(e.eras("short"),0);case"GG":return q(e.eras("long"),0);case"y":return w(c);case"yy":return w(h,pt);case"yyyy":return w(s);case"yyyyy":return w(g);case"yyyyyy":return w(o);case"M":return w(a);case"MM":return w(r);case"MMM":return q(e.months("short",!0),1);case"MMMM":return q(e.months("long",!0),1);case"L":return w(a);case"LL":return w(r);case"LLL":return q(e.months("short",!1),1);case"LLLL":return q(e.months("long",!1),1);case"d":return w(a);case"dd":return w(r);case"o":return w(u);case"ooo":return w(i);case"HH":return w(r);case"H":return w(a);case"hh":return w(r);case"h":return w(a);case"mm":return w(r);case"m":return w(a);case"q":return w(a);case"qq":return w(r);case"s":return w(a);case"ss":return w(r);case"S":return w(u);case"SSS":return w(i);case"u":return Zt(d);case"uu":return Zt(a);case"uuu":return w(t);case"a":return q(e.meridiems(),0);case"kkkk":return w(s);case"kk":return w(h,pt);case"W":return w(a);case"WW":return w(r);case"E":case"c":return w(t);case"EEE":return q(e.weekdays("short",!1),1);case"EEEE":return q(e.weekdays("long",!1),1);case"ccc":return q(e.weekdays("short",!0),1);case"cccc":return q(e.weekdays("long",!0),1);case"Z":case"ZZ":return Ui(new RegExp(`([+-]${a.source})(?::(${r.source}))?`),2);case"ZZZ":return Ui(new RegExp(`([+-]${a.source})(${r.source})?`),2);case"z":return Zt(/[a-z_+-/]{1,256}?/i);case" ":return Zt(/[^\S\n\r]/);default:return m(Y)}})(n)||{invalidReason:_o};return U.token=n,U}var Po={year:{"2-digit":"yy",numeric:"yyyyy"},month:{numeric:"M","2-digit":"MM",short:"MMM",long:"MMMM"},day:{numeric:"d","2-digit":"dd"},weekday:{short:"EEE",long:"EEEE"},dayperiod:"a",dayPeriod:"a",hour12:{numeric:"h","2-digit":"hh"},hour24:{numeric:"H","2-digit":"HH"},minute:{numeric:"m","2-digit":"mm"},second:{numeric:"s","2-digit":"ss"},timeZoneName:{long:"ZZZZZ",short:"ZZZ"}};function Ho(n,e,t){let{type:r,value:i}=n;if(r==="literal"){let u=/^\s+$/.test(i);return{literal:!u,val:u?" ":i}}let s=e[r],o=r;r==="hour"&&(e.hour12!=null?o=e.hour12?"hour12":"hour24":e.hourCycle!=null?e.hourCycle==="h11"||e.hourCycle==="h12"?o="hour12":o="hour24":o=t.hour12?"hour12":"hour24");let a=Po[o];if(typeof a=="object"&&(a=a[s]),a)return{literal:!1,val:a}}function Zo(n){return[`^${n.map(t=>t.regex).reduce((t,r)=>`${t}(${r.source})`,"")}$`,n]}function Go(n,e,t){let r=n.match(e);if(r){let i={},s=1;for(let o in t)if(ie(t,o)){let a=t[o],u=a.groups?a.groups+1:1;!a.literal&&a.token&&(i[a.token.val[0]]=a.deser(r.slice(s,s+u))),s+=u}return[r,i]}else return[r,{}]}function qo(n){let e=s=>{switch(s){case"S":return"millisecond";case"s":return"second";case"m":return"minute";case"h":case"H":return"hour";case"d":return"day";case"o":return"ordinal";case"L":case"M":return"month";case"y":return"year";case"E":case"c":return"weekday";case"W":return"weekNumber";case"k":return"weekYear";case"q":return"quarter";default:return null}},t=null,r;return p(n.z)||(t=A.create(n.z)),p(n.Z)||(t||(t=new F(n.Z)),r=n.Z),p(n.q)||(n.M=(n.q-1)*3+1),p(n.h)||(n.h<12&&n.a===1?n.h+=12:n.h===12&&n.a===0&&(n.h=0)),n.G===0&&n.y&&(n.y=-n.y),p(n.u)||(n.S=mt(n.u)),[Object.keys(n).reduce((s,o)=>{let a=e(o);return a&&(s[a]=n[o]),s},{}),t,r]}var ur=null;function Yo(){return ur||(ur=v.fromMillis(1555555555555)),ur}function Bo(n,e){if(n.literal)return n;let t=L.macroTokenToFormatOpts(n.val),r=dr(t,e);return r==null||r.includes(void 0)?n:r}function cr(n,e){return Array.prototype.concat(...n.map(t=>Bo(t,e)))}var vt=class{constructor(e,t){if(this.locale=e,this.format=t,this.tokens=cr(L.parseFormat(t),e),this.units=this.tokens.map(r=>Wo(r,e)),this.disqualifyingUnit=this.units.find(r=>r.invalidReason),!this.disqualifyingUnit){let[r,i]=Zo(this.units);this.regex=RegExp(r,"i"),this.handlers=i}}explainFromTokens(e){if(this.isValid){let[t,r]=Go(e,this.regex,this.handlers),[i,s,o]=r?qo(r):[null,null,void 0];if(ie(r,"a")&&ie(r,"H"))throw new W("Can't include meridiem when specifying 24-hour format");return{input:e,tokens:this.tokens,regex:this.regex,rawMatches:t,matches:r,result:i,zone:s,specificOffset:o}}else return{input:e,tokens:this.tokens,invalidReason:this.invalidReason}}get isValid(){return!this.disqualifyingUnit}get invalidReason(){return this.disqualifyingUnit?this.disqualifyingUnit.invalidReason:null}};function lr(n,e,t){return new vt(n,t).explainFromTokens(e)}function Hi(n,e,t){let{result:r,zone:i,specificOffset:s,invalidReason:o}=lr(n,e,t);return[r,i,s,o]}function dr(n,e){if(!n)return null;let r=L.create(e,n).dtFormatter(Yo()),i=r.formatToParts(),s=r.resolvedOptions();return i.map(o=>Ho(o,n,s))}var fr="Invalid DateTime",Zi=864e13;function Tt(n){return new x("unsupported zone",`the zone "${n.name}" is not supported`)}function hr(n){return n.weekData===null&&(n.weekData=dt(n.c)),n.weekData}function mr(n){return n.localWeekData===null&&(n.localWeekData=dt(n.c,n.loc.getMinDaysInFirstWeek(),n.loc.getStartOfWeek())),n.localWeekData}function pe(n,e){let t={ts:n.ts,zone:n.zone,c:n.c,o:n.o,loc:n.loc,invalid:n.invalid};return new v(b(l(l({},t),e),{old:t}))}function Qi(n,e,t){let r=n-e*60*1e3,i=t.offset(r);if(e===i)return[r,e];r-=(i-e)*60*1e3;let s=t.offset(r);return i===s?[r,i]:[n-Math.min(i,s)*60*1e3,Math.max(i,s)]}function Gt(n,e){n+=e*60*1e3;let t=new Date(n);return{year:t.getUTCFullYear(),month:t.getUTCMonth()+1,day:t.getUTCDate(),hour:t.getUTCHours(),minute:t.getUTCMinutes(),second:t.getUTCSeconds(),millisecond:t.getUTCMilliseconds()}}function Yt(n,e,t){return Qi(Se(n),e,t)}function Gi(n,e){let t=n.o,r=n.c.year+Math.trunc(e.years),i=n.c.month+Math.trunc(e.months)+Math.trunc(e.quarters)*3,s=b(l({},n.c),{year:r,month:i,day:Math.min(n.c.day,be(r,i))+Math.trunc(e.days)+Math.trunc(e.weeks)*7}),o=D.fromObject({years:e.years-Math.trunc(e.years),quarters:e.quarters-Math.trunc(e.quarters),months:e.months-Math.trunc(e.months),weeks:e.weeks-Math.trunc(e.weeks),days:e.days-Math.trunc(e.days),hours:e.hours,minutes:e.minutes,seconds:e.seconds,milliseconds:e.milliseconds}).as("milliseconds"),a=Se(s),[u,c]=Qi(a,t,n.zone);return o!==0&&(u+=o,c=n.zone.offset(u)),{ts:u,o:c}}function Le(n,e,t,r,i,s){let{setZone:o,zone:a}=t;if(n&&Object.keys(n).length!==0||e){let u=e||a,c=v.fromObject(n,b(l({},t),{zone:u,specificOffset:s}));return o?c:c.setZone(a)}else return v.invalid(new x("unparsable",`the input "${i}" can't be parsed as ${r}`))}function qt(n,e,t=!0){return n.isValid?L.create(T.create("en-US"),{allowZ:t,forceSimple:!0}).formatDateTimeFromString(n,e):null}function pr(n,e,t){let r=n.c.year>9999||n.c.year<0,i="";if(r&&n.c.year>=0&&(i+="+"),i+=O(n.c.year,r?6:4),t==="year")return i;if(e){if(i+="-",i+=O(n.c.month),t==="month")return i;i+="-"}else if(i+=O(n.c.month),t==="month")return i;return i+=O(n.c.day),i}function qi(n,e,t,r,i,s,o){let a=!t||n.c.millisecond!==0||n.c.second!==0,u="";switch(o){case"day":case"month":case"year":break;default:if(u+=O(n.c.hour),o==="hour")break;if(e){if(u+=":",u+=O(n.c.minute),o==="minute")break;a&&(u+=":",u+=O(n.c.second))}else{if(u+=O(n.c.minute),o==="minute")break;a&&(u+=O(n.c.second))}if(o==="second")break;a&&(!r||n.c.millisecond!==0)&&(u+=".",u+=O(n.c.millisecond,3))}return i&&(n.isOffsetFixed&&n.offset===0&&!s?u+="Z":n.o<0?(u+="-",u+=O(Math.trunc(-n.o/60)),u+=":",u+=O(Math.trunc(-n.o%60))):(u+="+",u+=O(Math.trunc(n.o/60)),u+=":",u+=O(Math.trunc(n.o%60)))),s&&(u+="["+n.zone.ianaName+"]"),u}var Xi={month:1,day:1,hour:0,minute:0,second:0,millisecond:0},Ko={weekNumber:1,weekday:1,hour:0,minute:0,second:0,millisecond:0},Jo={ordinal:1,hour:0,minute:0,second:0,millisecond:0},Bt=["year","month","day","hour","minute","second","millisecond"],Qo=["weekYear","weekNumber","weekday","hour","minute","second","millisecond"],Xo=["year","ordinal","hour","minute","second","millisecond"];function Kt(n){let e={year:"year",years:"year",month:"month",months:"month",day:"day",days:"day",hour:"hour",hours:"hour",minute:"minute",minutes:"minute",quarter:"quarter",quarters:"quarter",second:"second",seconds:"second",millisecond:"millisecond",milliseconds:"millisecond",weekday:"weekday",weekdays:"weekday",weeknumber:"weekNumber",weeksnumber:"weekNumber",weeknumbers:"weekNumber",weekyear:"weekYear",weekyears:"weekYear",ordinal:"ordinal"}[n.toLowerCase()];if(!e)throw new we(n);return e}function Yi(n){switch(n.toLowerCase()){case"localweekday":case"localweekdays":return"localWeekday";case"localweeknumber":case"localweeknumbers":return"localWeekNumber";case"localweekyear":case"localweekyears":return"localWeekYear";default:return Kt(n)}}function ea(n){if(wt===void 0&&(wt=y.now()),n.type!=="iana")return n.offset(wt);let e=n.name,t=gr.get(e);return t===void 0&&(t=n.offset(wt),gr.set(e,t)),t}function Bi(n,e){let t=Z(e.zone,y.defaultZone);if(!t.isValid)return v.invalid(Tt(t));let r=T.fromObject(e),i,s;if(p(n.year))i=y.now();else{for(let u of Bt)p(n[u])&&(n[u]=Xi[u]);let o=Qn(n)||Xn(n);if(o)return v.invalid(o);let a=ea(t);[i,s]=Yt(n,a,t)}return new v({ts:i,zone:t,loc:r,o:s})}function Ki(n,e,t){let r=p(t.round)?!0:t.round,i=p(t.rounding)?"trunc":t.rounding,s=(a,u)=>(a=Ee(a,r||t.calendary?0:2,t.calendary?"round":i),e.loc.clone(t).relFormatter(t).format(a,u)),o=a=>t.calendary?e.hasSame(n,a)?0:e.startOf(a).diff(n.startOf(a),a).get(a):e.diff(n,a).get(a);if(t.unit)return s(o(t.unit),t.unit);for(let a of t.units){let u=o(a);if(Math.abs(u)>=1)return s(u,a)}return s(n>e?-0:0,t.units[t.units.length-1])}function Ji(n){let e={},t;return n.length>0&&typeof n[n.length-1]=="object"?(e=n[n.length-1],t=Array.from(n).slice(0,n.length-1)):t=Array.from(n),[e,t]}var wt,gr=new Map,v=class n{constructor(e){let t=e.zone||y.defaultZone,r=e.invalid||(Number.isNaN(e.ts)?new x("invalid input"):null)||(t.isValid?null:Tt(t));this.ts=p(e.ts)?y.now():e.ts;let i=null,s=null;if(!r)if(e.old&&e.old.ts===this.ts&&e.old.zone.equals(t))[i,s]=[e.old.c,e.old.o];else{let a=G(e.o)&&!e.old?e.o:t.offset(this.ts);i=Gt(this.ts,a),r=Number.isNaN(i.year)?new x("invalid input"):null,i=r?null:i,s=r?null:a}this._zone=t,this.loc=e.loc||T.create(),this.invalid=r,this.weekData=null,this.localWeekData=null,this.c=i,this.o=s,this.isLuxonDateTime=!0}static now(){return new n({})}static local(){let[e,t]=Ji(arguments),[r,i,s,o,a,u,c]=t;return Bi({year:r,month:i,day:s,hour:o,minute:a,second:u,millisecond:c},e)}static utc(){let[e,t]=Ji(arguments),[r,i,s,o,a,u,c]=t;return e.zone=F.utcInstance,Bi({year:r,month:i,day:s,hour:o,minute:a,second:u,millisecond:c},e)}static fromJSDate(e,t={}){let r=fi(e)?e.valueOf():NaN;if(Number.isNaN(r))return n.invalid("invalid input");let i=Z(t.zone,y.defaultZone);return i.isValid?new n({ts:r,zone:i,loc:T.fromObject(t)}):n.invalid(Tt(i))}static fromMillis(e,t={}){if(G(e))return e<-Zi||e>Zi?n.invalid("Timestamp out of range"):new n({ts:e,zone:Z(t.zone,y.defaultZone),loc:T.fromObject(t)});throw new k(`fromMillis requires a numerical input, but received a ${typeof e} with value ${e}`)}static fromSeconds(e,t={}){if(G(e))return new n({ts:e*1e3,zone:Z(t.zone,y.defaultZone),loc:T.fromObject(t)});throw new k("fromSeconds requires a numerical input")}static fromObject(e,t={}){e=e||{};let r=Z(t.zone,y.defaultZone);if(!r.isValid)return n.invalid(Tt(r));let i=T.fromObject(t),s=Ie(e,Yi),{minDaysInFirstWeek:o,startOfWeek:a}=Jn(s,i),u=y.now(),c=p(t.specificOffset)?r.offset(u):t.specificOffset,d=!p(s.ordinal),h=!p(s.year),g=!p(s.month)||!p(s.day),m=h||g,M=s.weekYear||s.weekNumber;if((m||d)&&M)throw new W("Can't mix weekYear/weekNumber units with year/month/day or ordinals");if(g&&d)throw new W("Can't mix ordinal dates with month/day");let U=M||s.weekday&&!m,Y,ve,Ae=Gt(u,c);U?(Y=Qo,ve=Ko,Ae=dt(Ae,o,a)):d?(Y=Xo,ve=Jo,Ae=Ht(Ae)):(Y=Bt,ve=Xi);let Mr=!1;for(let $e of Y){let Os=s[$e];p(Os)?Mr?s[$e]=ve[$e]:s[$e]=Ae[$e]:Mr=!0}let Ss=U?ci(s,o,a):d?li(s):Qn(s),Cr=Ss||Xn(s);if(Cr)return n.invalid(Cr);let Es=U?Bn(s,o,a):d?Kn(s):s,[bs,Is]=Yt(Es,c,r),Re=new n({ts:bs,zone:r,o:Is,loc:i});return s.weekday&&m&&e.weekday!==Re.weekday?n.invalid("mismatched weekday",`you can't specify both a weekday of ${s.weekday} and a date of ${Re.toISO()}`):Re.isValid?Re:n.invalid(Re.invalid)}static fromISO(e,t={}){let[r,i]=Di(e);return Le(r,i,t,"ISO 8601",e)}static fromRFC2822(e,t={}){let[r,i]=Mi(e);return Le(r,i,t,"RFC 2822",e)}static fromHTTP(e,t={}){let[r,i]=Ci(e);return Le(r,i,t,"HTTP",t)}static fromFormat(e,t,r={}){if(p(e)||p(t))throw new k("fromFormat requires an input string and a format");let{locale:i=null,numberingSystem:s=null}=r,o=T.fromOpts({locale:i,numberingSystem:s,defaultToEN:!0}),[a,u,c,d]=Hi(o,e,t);return d?n.invalid(d):Le(a,u,r,`format ${t}`,e,c)}static fromString(e,t,r={}){return n.fromFormat(e,t,r)}static fromSQL(e,t={}){let[r,i]=xi(e);return Le(r,i,t,"SQL",e)}static invalid(e,t=null){if(!e)throw new k("need to specify a reason the DateTime is invalid");let r=e instanceof x?e:new x(e,t);if(y.throwOnInvalid)throw new At(r);return new n({invalid:r})}static isDateTime(e){return e&&e.isLuxonDateTime||!1}static parseFormatForOpts(e,t={}){let r=dr(e,T.fromObject(t));return r?r.map(i=>i?i.val:null).join(""):null}static expandFormat(e,t={}){return cr(L.parseFormat(e),T.fromObject(t)).map(i=>i.val).join("")}static resetCache(){wt=void 0,gr.clear()}get(e){return this[e]}get isValid(){return this.invalid===null}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}get locale(){return this.isValid?this.loc.locale:null}get numberingSystem(){return this.isValid?this.loc.numberingSystem:null}get outputCalendar(){return this.isValid?this.loc.outputCalendar:null}get zone(){return this._zone}get zoneName(){return this.isValid?this.zone.name:null}get year(){return this.isValid?this.c.year:NaN}get quarter(){return this.isValid?Math.ceil(this.c.month/3):NaN}get month(){return this.isValid?this.c.month:NaN}get day(){return this.isValid?this.c.day:NaN}get hour(){return this.isValid?this.c.hour:NaN}get minute(){return this.isValid?this.c.minute:NaN}get second(){return this.isValid?this.c.second:NaN}get millisecond(){return this.isValid?this.c.millisecond:NaN}get weekYear(){return this.isValid?hr(this).weekYear:NaN}get weekNumber(){return this.isValid?hr(this).weekNumber:NaN}get weekday(){return this.isValid?hr(this).weekday:NaN}get isWeekend(){return this.isValid&&this.loc.getWeekendDays().includes(this.weekday)}get localWeekday(){return this.isValid?mr(this).weekday:NaN}get localWeekNumber(){return this.isValid?mr(this).weekNumber:NaN}get localWeekYear(){return this.isValid?mr(this).weekYear:NaN}get ordinal(){return this.isValid?Ht(this.c).ordinal:NaN}get monthShort(){return this.isValid?ae.months("short",{locObj:this.loc})[this.month-1]:null}get monthLong(){return this.isValid?ae.months("long",{locObj:this.loc})[this.month-1]:null}get weekdayShort(){return this.isValid?ae.weekdays("short",{locObj:this.loc})[this.weekday-1]:null}get weekdayLong(){return this.isValid?ae.weekdays("long",{locObj:this.loc})[this.weekday-1]:null}get offset(){return this.isValid?+this.o:NaN}get offsetNameShort(){return this.isValid?this.zone.offsetName(this.ts,{format:"short",locale:this.locale}):null}get offsetNameLong(){return this.isValid?this.zone.offsetName(this.ts,{format:"long",locale:this.locale}):null}get isOffsetFixed(){return this.isValid?this.zone.isUniversal:null}get isInDST(){return this.isOffsetFixed?!1:this.offset>this.set({month:1,day:1}).offset||this.offset>this.set({month:5}).offset}getPossibleOffsets(){if(!this.isValid||this.isOffsetFixed)return[this];let e=864e5,t=6e4,r=Se(this.c),i=this.zone.offset(r-e),s=this.zone.offset(r+e),o=this.zone.offset(r-i*t),a=this.zone.offset(r-s*t);if(o===a)return[this];let u=r-o*t,c=r-a*t,d=Gt(u,o),h=Gt(c,a);return d.hour===h.hour&&d.minute===h.minute&&d.second===h.second&&d.millisecond===h.millisecond?[pe(this,{ts:u}),pe(this,{ts:c})]:[this]}get isInLeapYear(){return he(this.year)}get daysInMonth(){return be(this.year,this.month)}get daysInYear(){return this.isValid?re(this.year):NaN}get weeksInWeekYear(){return this.isValid?fe(this.weekYear):NaN}get weeksInLocalWeekYear(){return this.isValid?fe(this.localWeekYear,this.loc.getMinDaysInFirstWeek(),this.loc.getStartOfWeek()):NaN}resolvedLocaleOptions(e={}){let{locale:t,numberingSystem:r,calendar:i}=L.create(this.loc.clone(e),e).resolvedOptions(this);return{locale:t,numberingSystem:r,outputCalendar:i}}toUTC(e=0,t={}){return this.setZone(F.instance(e),t)}toLocal(){return this.setZone(y.defaultZone)}setZone(e,{keepLocalTime:t=!1,keepCalendarTime:r=!1}={}){if(e=Z(e,y.defaultZone),e.equals(this.zone))return this;if(e.isValid){let i=this.ts;if(t||r){let s=e.offset(this.ts),o=this.toObject();[i]=Yt(o,s,e)}return pe(this,{ts:i,zone:e})}else return n.invalid(Tt(e))}reconfigure({locale:e,numberingSystem:t,outputCalendar:r}={}){let i=this.loc.clone({locale:e,numberingSystem:t,outputCalendar:r});return pe(this,{loc:i})}setLocale(e){return this.reconfigure({locale:e})}set(e){if(!this.isValid)return this;let t=Ie(e,Yi),{minDaysInFirstWeek:r,startOfWeek:i}=Jn(t,this.loc),s=!p(t.weekYear)||!p(t.weekNumber)||!p(t.weekday),o=!p(t.ordinal),a=!p(t.year),u=!p(t.month)||!p(t.day),c=a||u,d=t.weekYear||t.weekNumber;if((c||o)&&d)throw new W("Can't mix weekYear/weekNumber units with year/month/day or ordinals");if(u&&o)throw new W("Can't mix ordinal dates with month/day");let h;s?h=Bn(l(l({},dt(this.c,r,i)),t),r,i):p(t.ordinal)?(h=l(l({},this.toObject()),t),p(t.day)&&(h.day=Math.min(be(h.year,h.month),h.day))):h=Kn(l(l({},Ht(this.c)),t));let[g,m]=Yt(h,this.o,this.zone);return pe(this,{ts:g,o:m})}plus(e){if(!this.isValid)return this;let t=D.fromDurationLike(e);return pe(this,Gi(this,t))}minus(e){if(!this.isValid)return this;let t=D.fromDurationLike(e).negate();return pe(this,Gi(this,t))}startOf(e,{useLocaleWeeks:t=!1}={}){if(!this.isValid)return this;let r={},i=D.normalizeUnit(e);switch(i){case"years":r.month=1;case"quarters":case"months":r.day=1;case"weeks":case"days":r.hour=0;case"hours":r.minute=0;case"minutes":r.second=0;case"seconds":r.millisecond=0;break;case"milliseconds":break}if(i==="weeks")if(t){let s=this.loc.getStartOfWeek(),{weekday:o}=this;o<s&&(r.weekNumber=this.weekNumber-1),r.weekday=s}else r.weekday=1;if(i==="quarters"){let s=Math.ceil(this.month/3);r.month=(s-1)*3+1}return this.set(r)}endOf(e,t){return this.isValid?this.plus({[e]:1}).startOf(e,t).minus(1):this}toFormat(e,t={}){return this.isValid?L.create(this.loc.redefaultToEN(t)).formatDateTimeFromString(this,e):fr}toLocaleString(e=te,t={}){return this.isValid?L.create(this.loc.clone(t),e).formatDateTime(this):fr}toLocaleParts(e={}){return this.isValid?L.create(this.loc.clone(e),e).formatDateTimeParts(this):[]}toISO({format:e="extended",suppressSeconds:t=!1,suppressMilliseconds:r=!1,includeOffset:i=!0,extendedZone:s=!1,precision:o="milliseconds"}={}){if(!this.isValid)return null;o=Kt(o);let a=e==="extended",u=pr(this,a,o);return Bt.indexOf(o)>=3&&(u+="T"),u+=qi(this,a,t,r,i,s,o),u}toISODate({format:e="extended",precision:t="day"}={}){return this.isValid?pr(this,e==="extended",Kt(t)):null}toISOWeekDate(){return qt(this,"kkkk-'W'WW-c")}toISOTime({suppressMilliseconds:e=!1,suppressSeconds:t=!1,includeOffset:r=!0,includePrefix:i=!1,extendedZone:s=!1,format:o="extended",precision:a="milliseconds"}={}){return this.isValid?(a=Kt(a),(i&&Bt.indexOf(a)>=3?"T":"")+qi(this,o==="extended",t,e,r,s,a)):null}toRFC2822(){return qt(this,"EEE, dd LLL yyyy HH:mm:ss ZZZ",!1)}toHTTP(){return qt(this.toUTC(),"EEE, dd LLL yyyy HH:mm:ss 'GMT'")}toSQLDate(){return this.isValid?pr(this,!0):null}toSQLTime({includeOffset:e=!0,includeZone:t=!1,includeOffsetSpace:r=!0}={}){let i="HH:mm:ss.SSS";return(t||e)&&(r&&(i+=" "),t?i+="z":e&&(i+="ZZ")),qt(this,i,!0)}toSQL(e={}){return this.isValid?`${this.toSQLDate()} ${this.toSQLTime(e)}`:null}toString(){return this.isValid?this.toISO():fr}[Symbol.for("nodejs.util.inspect.custom")](){return this.isValid?`DateTime { ts: ${this.toISO()}, zone: ${this.zone.name}, locale: ${this.locale} }`:`DateTime { Invalid, reason: ${this.invalidReason} }`}valueOf(){return this.toMillis()}toMillis(){return this.isValid?this.ts:NaN}toSeconds(){return this.isValid?this.ts/1e3:NaN}toUnixInteger(){return this.isValid?Math.floor(this.ts/1e3):NaN}toJSON(){return this.toISO()}toBSON(){return this.toJSDate()}toObject(e={}){if(!this.isValid)return{};let t=l({},this.c);return e.includeConfig&&(t.outputCalendar=this.outputCalendar,t.numberingSystem=this.loc.numberingSystem,t.locale=this.loc.locale),t}toJSDate(){return new Date(this.isValid?this.ts:NaN)}diff(e,t="milliseconds",r={}){if(!this.isValid||!e.isValid)return D.invalid("created by diffing an invalid DateTime");let i=l({locale:this.locale,numberingSystem:this.numberingSystem},r),s=hi(t).map(D.normalizeUnit),o=e.valueOf()>this.valueOf(),a=o?this:e,u=o?e:this,c=ji(a,u,s,i);return o?c.negate():c}diffNow(e="milliseconds",t={}){return this.diff(n.now(),e,t)}until(e){return this.isValid?oe.fromDateTimes(this,e):this}hasSame(e,t,r){if(!this.isValid)return!1;let i=e.valueOf(),s=this.setZone(e.zone,{keepLocalTime:!0});return s.startOf(t,r)<=i&&i<=s.endOf(t,r)}equals(e){return this.isValid&&e.isValid&&this.valueOf()===e.valueOf()&&this.zone.equals(e.zone)&&this.loc.equals(e.loc)}toRelative(e={}){if(!this.isValid)return null;let t=e.base||n.fromObject({},{zone:this.zone}),r=e.padding?this<t?-e.padding:e.padding:0,i=["years","months","days","hours","minutes","seconds"],s=e.unit;return Array.isArray(e.unit)&&(i=e.unit,s=void 0),Ki(t,this.plus(r),b(l({},e),{numeric:"always",units:i,unit:s}))}toRelativeCalendar(e={}){return this.isValid?Ki(e.base||n.fromObject({},{zone:this.zone}),this,b(l({},e),{numeric:"auto",units:["years","months","days"],calendary:!0})):null}static min(...e){if(!e.every(n.isDateTime))throw new k("min requires all arguments be DateTimes");return er(e,t=>t.valueOf(),Math.min)}static max(...e){if(!e.every(n.isDateTime))throw new k("max requires all arguments be DateTimes");return er(e,t=>t.valueOf(),Math.max)}static fromFormatExplain(e,t,r={}){let{locale:i=null,numberingSystem:s=null}=r,o=T.fromOpts({locale:i,numberingSystem:s,defaultToEN:!0});return lr(o,e,t)}static fromStringExplain(e,t,r={}){return n.fromFormatExplain(e,t,r)}static buildFormatParser(e,t={}){let{locale:r=null,numberingSystem:i=null}=t,s=T.fromOpts({locale:r,numberingSystem:i,defaultToEN:!0});return new vt(s,e)}static fromFormatParser(e,t,r={}){if(p(e)||p(t))throw new k("fromFormatParser requires an input string and a format parser");let{locale:i=null,numberingSystem:s=null}=r,o=T.fromOpts({locale:i,numberingSystem:s,defaultToEN:!0});if(!o.equals(t.locale))throw new k(`fromFormatParser called with a locale of ${o}, but the format parser was created for ${t.locale}`);let{result:a,zone:u,specificOffset:c,invalidReason:d}=t.explainFromTokens(e);return d?n.invalid(d):Le(a,u,r,`format ${t.format}`,e,c)}static get DATE_SHORT(){return te}static get DATE_MED(){return Pe}static get DATE_MED_WITH_WEEKDAY(){return Dn}static get DATE_FULL(){return He}static get DATE_HUGE(){return Ze}static get TIME_SIMPLE(){return Ge}static get TIME_WITH_SECONDS(){return qe}static get TIME_WITH_SHORT_OFFSET(){return Ye}static get TIME_WITH_LONG_OFFSET(){return Be}static get TIME_24_SIMPLE(){return Ke}static get TIME_24_WITH_SECONDS(){return Je}static get TIME_24_WITH_SHORT_OFFSET(){return Qe}static get TIME_24_WITH_LONG_OFFSET(){return Xe}static get DATETIME_SHORT(){return et}static get DATETIME_SHORT_WITH_SECONDS(){return tt}static get DATETIME_MED(){return nt}static get DATETIME_MED_WITH_SECONDS(){return rt}static get DATETIME_MED_WITH_WEEKDAY(){return Mn}static get DATETIME_FULL(){return it}static get DATETIME_FULL_WITH_SECONDS(){return st}static get DATETIME_HUGE(){return ot}static get DATETIME_HUGE_WITH_SECONDS(){return at}};function xe(n){if(v.isDateTime(n))return n;if(n&&n.valueOf&&G(n.valueOf()))return v.fromJSDate(n);if(n&&typeof n=="object")return v.fromObject(n);throw new k(`Unknown datetime argument: ${n}, of type ${typeof n}`)}var ia=(()=>{class n{warningMessage(){console.error("You should add @abp/ng-oauth packages or create your own auth packages.")}get oidc(){return this.warningMessage(),!1}set oidc(t){this.warningMessage()}init(){return this.warningMessage(),Promise.resolve(void 0)}login(t){return this.warningMessage(),kt(void 0)}logout(t){return this.warningMessage(),kt(void 0)}navigateToLogin(t){}get isInternalAuth(){throw new Error("not implemented")}get isAuthenticated(){return this.warningMessage(),!1}loginUsingGrant(t,r,i){return console.log({grantType:t,parameters:r,headers:i}),Promise.reject(new Error("not implemented"))}getAccessTokenExpiration(){return this.warningMessage(),0}getRefreshToken(){return this.warningMessage(),""}getAccessToken(){return this.warningMessage(),""}refreshToken(){return this.warningMessage(),Promise.resolve(void 0)}static{this.\u0275fac=function(r){return new(r||n)}}static{this.\u0275prov=I({token:n,factory:n.\u0275fac,providedIn:"root"})}}return n})(),yr=class{},vr=class extends yr{warningMessage(){console.error("You should add @abp/ng-oauth packages or create your own auth packages.")}get(e){throw this.warningMessage(),new Error("not implemented")}add(e){this.warningMessage()}patch(e){this.warningMessage()}remove(e){this.warningMessage()}run(e){throw this.warningMessage(),new Error("not implemented")}},us=new C("LOCALIZATIONS");function cs(n){n&&Tr.next([...Tr.value,...n])}var Tr=new B([]),bt=new C("CORE_OPTIONS");function sa(e){var n=ue(e,[]);return l({},n)}function wr(n){return(e,t,r)=>{if(e==="_")return t;let i=n?.values?.[e];return i&&i[t]||r}}function es(n){let e=oa(n);return(t,r,i)=>{let{localized:s}=e(t,r);return s||i}}function oa(n){let e=wr(n);return(t,r)=>{t=t.concat(n.defaultResourceName||"").filter(Boolean);let i=t.length,s=r.length;for(let o=0;o<i;o++){let a=t[o];for(let u=0;u<s;u++){let c=r[u],d=e(a,c,null);if(d)return{resourceName:a,key:c,localized:d}}}return{resourceName:void 0,key:void 0,localized:void 0}}}function aa(n){return e=>{let t=[],r=n.replace(/\./g,"\\.").replace(/\{\s?([0-9a-zA-Z]+)\s?\}/g,(s,o)=>(t.push(o),"(.+)"));return(e.match(r)||[]).slice(1).reduce((s,o,a)=>{let u=t[a];return s[u]=[...s[u]||[],o].filter(Boolean),s},{})}}function ua(n,e){return n.replace(/(['"])?\{\s*(\d+)\s*\}\1/g,(t,r,i)=>(r||"")+(e[i]??`{${i}}`)+(r||"")).replace(/\s+/g," ")}function ca(n){return e=>(n.push(e),n)}function la(){return function(){}}function da(n){return n===void 0||n===""}function St(n){return n==null}function ls(n){return!St(n)}function Sr(n){return n instanceof Object}function Jt(n){return Array.isArray(n)}function fa(n){return Sr(n)&&!Jt(n)}function Er(n){return n instanceof Node}function ts(n){return fa(n)&&!Er(n)}function ha(n,e){return Object.prototype.hasOwnProperty.call(n,e)}function ds(n,e){return ts(n)&&ts(e)?fs(n,e):St(n)&&St(e)?{}:ls(e)?e:n}function fs(n,e){if(St(n)||St(e)||Jt(n)||Jt(e)||!Sr(n)||!Sr(e)||Er(n)||Er(e))return ls(e)?e:n;let r=Object.keys(n),i=Object.keys(e);return[...new Set(r.concat(i))].reduce((o,a)=>(o[a]=fs(n[a],e[a]),o),{})}var Et=class{get state(){return this.state$.value}constructor(e){this.initialState=e,this.state$=new B(this.initialState),this.update$=new ze,this.sliceState=(t,r=On)=>this.state$.pipe(E(t),on(r)),this.sliceUpdate=(t,r=i=>i!==void 0)=>this.update$.pipe(E(t),ce(r))}patch(e){let t=e;typeof e=="object"&&!Array.isArray(e)&&(t=l(l({},this.state),e)),this.state$.next(t),this.update$.next(t)}deepPatch(e){this.state$.next(ds(this.state,e)),this.update$.next(e)}set(e){this.state$.next(e),this.update$.next(e)}reset(){this.set(this.initialState)}},ns=n=>e=>(n&&e[n]||e.default).url||e.default.url,rs=n=>n&&(n.endsWith("/")?n:n+"/"),It=(()=>{class n{constructor(){this.store=new Et({})}get createOnUpdateStream(){return this.store.sliceUpdate}getEnvironment$(){return this.store.sliceState(t=>t)}getEnvironment(){return this.store.state}getApiUrl(t){return ns(t)(this.store.state?.apis)}getApiUrl$(t){return this.store.sliceState(r=>r.apis).pipe(E(ns(t)))}setState(t){this.store.set(t)}getIssuer(){let t=this.store.state?.oAuthConfig?.issuer;return rs(t)}getIssuer$(){return this.store.sliceState(t=>t?.oAuthConfig?.issuer).pipe(E(rs))}getImpersonation(){return this.store.state?.oAuthConfig?.impersonation||{}}getImpersonation$(){return this.store.sliceState(t=>t?.oAuthConfig?.impersonation||{})}static{this.\u0275fac=function(r){return new(r||n)}}static{this.\u0275prov=I({token:n,factory:n.\u0275fac,providedIn:"root"})}}return n})(),hs=(()=>{class n{constructor(){this._reporter$=new ze,this._errors$=new B([])}get reporter$(){return this._reporter$.asObservable()}get errors$(){return this._errors$.asObservable()}get errors(){return this._errors$.value}reportError(t){this._reporter$.next(t),this._errors$.next([...this.errors,t])}static{this.\u0275fac=function(r){return new(r||n)}}static{this.\u0275prov=I({token:n,factory:n.\u0275fac,providedIn:"root"})}}return n})();function ma(n,e){let t=n.get(It),{remoteEnv:r}=e,{headers:i={},method:s="GET",url:o}=r||{};if(!o)return Promise.resolve();let a=n.get(Ue),u=n.get(hs);return a.request(s,o,{headers:i}).pipe(Mt(c=>(u.reportError(c),kt(null))),Te(c=>t.setState(pa(e,c||{},r)))).toPromise()}function pa(n,e,t){switch(t.mergeStrategy){case"deepmerge":return ds(n,e);case"overwrite":case null:case void 0:return e;default:return t.mergeStrategy(n,e)}}function ga(n){return n==Number(n)}function Yd(n){let e=[];for(let t in n)ga(t)||e.push({key:t,value:n[t]});return e}var ya=(()=>{class n{constructor(){}get length(){return localStorage.length}clear(){localStorage.clear()}getItem(t){return localStorage.getItem(t)}key(t){return localStorage.key(t)}removeItem(t){localStorage.removeItem(t)}setItem(t,r){localStorage.setItem(t,r)}static{this.\u0275fac=function(r){return new(r||n)}}static{this.\u0275prov=I({token:n,factory:n.\u0275fac,providedIn:"root"})}}return n})(),Ot=(()=>{class n{constructor(t,r){this.configState=t,this.localStorageService=r,this.store=new Et({}),this.document=N(Ve),this.updateLocalStorage=()=>{this.localStorageService.setItem("abpSession",JSON.stringify(this.store.state))},this.init(),this.setInitialLanguage()}init(){let t=this.localStorageService.getItem("abpSession");t&&this.store.set(JSON.parse(t)),this.store.sliceUpdate(r=>r).subscribe(this.updateLocalStorage)}setInitialLanguage(){let t=this.getLanguage();this.configState.getDeep$("localization.currentCulture.cultureName").pipe(ce(r=>!!r),Ct(1)).subscribe(r=>{r.includes(";")&&(r=r.split(";")[0]),this.setLanguage(r)})}onLanguageChange$(){return this.store.sliceUpdate(t=>t.language)}onTenantChange$(){return this.store.sliceUpdate(t=>t.tenant)}getLanguage(){return this.store.state.language}getLanguage$(){return this.store.sliceState(t=>t.language)}getTenant(){return this.store.state.tenant}getTenant$(){return this.store.sliceState(t=>t.tenant)}setTenant(t){On(t,this.store.state.tenant)||this.store.set(b(l({},this.store.state),{tenant:t}))}setLanguage(t){let r=this.store.state.language;t!==r&&this.store.patch({language:t});let i=this.document.documentElement.getAttribute("lang");t!==i&&this.document.documentElement.setAttribute("lang",t)}static{this.\u0275fac=function(r){return new(r||n)(S(ge),S(ya))}}static{this.\u0275prov=I({token:n,factory:n.\u0275fac,providedIn:"root"})}}return n})(),va=new C("APP_INIT_ERROR_HANDLERS"),Ta=(()=>{class n{constructor(t){this.restService=t,this.apiName="abp",this.findTenantById=(r,i)=>this.restService.request({method:"GET",url:`/api/abp/multi-tenancy/tenants/by-id/${r}`},l({apiName:this.apiName},i)),this.findTenantByName=(r,i)=>this.restService.request({method:"GET",url:`/api/abp/multi-tenancy/tenants/by-name/${r}`},l({apiName:this.apiName},i))}static{this.\u0275fac=function(r){return new(r||n)(S(en))}}static{this.\u0275prov=I({token:n,factory:n.\u0275fac,providedIn:"root"})}}return n})(),ms=new C("TENANT_KEY"),wa=new $r(()=>!1),Sa=(()=>{class n extends Ue{request(t,r,i={}){return typeof t=="string"?(this.#e(i),super.request(t,r||"",i)):(this.#e(t),super.request(t))}#e(t){t.context??=new zr,t.context.set(wa,!0)}static{this.\u0275fac=(()=>{let t;return function(i){return(t||(t=ln(n)))(i||n)}})()}static{this.\u0275prov=I({token:n,factory:n.\u0275fac,providedIn:"root"})}}return n})(),en=(()=>{class n{constructor(t,r,i,s,o){this.options=t,this.http=r,this.externalHttp=i,this.environment=s,this.httpErrorReporter=o}getApiFromStore(t){return this.environment.getApiUrl(t)}handleError(t){return this.httpErrorReporter.reportError(t),Dt(()=>t)}request(t,r,i){r=r||{},i=i||this.getApiFromStore(r.apiName);let g=t,{method:s,params:o}=g,a=ue(g,["method","params"]),{observe:u="body",skipHandleError:c}=r,d=this.removeDuplicateSlashes(i+t.url);return this.getHttpClient(r.skipAddingHeader).request(s,d,l(l({observe:u},o&&{params:this.getParams(o,r.httpParamEncoder)}),a)).pipe(Mt(m=>c?Dt(()=>m):this.handleError(m)))}getHttpClient(t){return t?this.externalHttp:this.http}getParams(t,r){let i=Object.entries(t).reduce((s,[o,a])=>(da(a)||a===null&&!this.options.sendNullsAsQueryParam||(s[o]=a),s),{});return r?new gn({encoder:r,fromObject:i}):new gn({fromObject:i})}removeDuplicateSlashes(t){return t.replace(/([^:]\/)\/+/g,"$1")}static{this.\u0275fac=function(r){return new(r||n)(S(bt),S(Ue),S(Sa),S(It),S(hs))}}static{this.\u0275prov=I({token:n,factory:n.\u0275fac,providedIn:"root"})}}return n})(),Ea=(()=>{class n{constructor(t,r,i,s,o){this.restService=t,this.sessionState=r,this.tenantService=i,this.configStateService=s,this.tenantKey=o,this.domainTenant=null,this.isTenantBoxVisible=!0,this.apiName="abp",this.setTenantToState=a=>(this.sessionState.setTenant({id:a.tenantId,name:a.name,isAvailable:!0}),this.configStateService.refreshAppState().pipe(E(u=>a)))}setTenantByName(t){return this.tenantService.findTenantByName(t).pipe(ee(this.setTenantToState))}setTenantById(t){return this.tenantService.findTenantById(t).pipe(ee(this.setTenantToState))}static{this.\u0275fac=function(r){return new(r||n)(S(en),S(Ot),S(Ta),S(ge),S(ms))}}static{this.\u0275prov=I({token:n,factory:n.\u0275fac,providedIn:"root"})}}return n})(),ba=new C("TENANT_NOT_FOUND_BY_NAME"),Dr="{0}";function Ia(n){n.charAt(n.length-1)!=="/"&&(n+="/");let e=aa(n),t=Dr.replace(/[}{]/g,"");return e(window.location.href)[t]?.[0]}function Oa(n){return new URLSearchParams(window.location.search).get(n)}function ka(n){return tn(this,null,function*(){let e=n.get(It),t=n.get(Ea),r=n.get(ba,null),i=e.getEnvironment()?.application?.baseUrl||"",s=Ia(i),o=()=>{t.isTenantBoxVisible=!1},a=d=>{t.domainTenant={id:d.tenantId,name:d.name,isAvailable:!0}},u=d=>{o(),a(d)};if(s){is(n,s);let d=t.setTenantByName(s);try{let h=yield rn(d);return u(h),Promise.resolve(h)}catch(h){return h instanceof _r&&h.status===404&&r&&r(h),Promise.reject()}}is(n,"",Dr+".");let c=Oa(t.tenantKey);if(c){let d=t.setTenantById(c);return rn(d)}return Promise.resolve()})}function is(n,e,t=Dr){let r=n.get(It),i=Wr(r.getEnvironment());return i.application.baseUrl&&(i.application.baseUrl=i.application.baseUrl.replace(t,e)),i.oAuthConfig?.redirectUri&&(i.oAuthConfig.redirectUri=i.oAuthConfig.redirectUri.replace(t,e)),i.oAuthConfig||(i.oAuthConfig={}),i.oAuthConfig.issuer=(i.oAuthConfig.issuer||"").replace(t,e),Object.keys(i.apis).forEach(s=>{Object.keys(i.apis[s]).forEach(o=>{i.apis[s][o]=(i.apis[s][o]||"").replace(t,e)})}),r.setState(i)}var Da=new C("CHECK_AUTHENTICATION_STATE_FN_KEY");function Ma(){return tn(this,null,function*(){let n=N(K),e=n.get(It),t=n.get(ge),r=n.get(bt);e.setState(r.environment),yield ma(n,r.environment),yield ka(n);let i=n.get(ia,void 0,{optional:!0}),s=n.get(Da,la,{optional:!0});if(!r.skipInitAuthService&&i&&(yield i.init()),r.skipGetAppConfiguration)return;let o=t.refreshAppState().pipe(Te(()=>s(n)),Te(()=>{let a=t.getOne("currentTenant");n.get(Ot).setTenant(a)}),Mt(a=>{let u=n.get(va,null);return u&&u.length&&u.forEach(c=>c(a)),Dt(()=>a)}));yield Fr(o)})}function Ca(){let n=N(K),e=n.get(Ot),{registerLocaleFn:t}=n.get(bt),r=e.getLanguage()||"en";return new Promise((i,s)=>{t(r).then(o=>(o?.default&&pn(o.default),i("resolved")),s)})}var br=class{constructor(){this.queue=[],this.isRunning=!1,this.stack=0,this.interval=0,this.stackSize=100}init(e,t){this.interval=e,this.stackSize=t}add(e){this.queue.push(e),this.run()}run(){if(this.isRunning)return;this.stack++,this.isRunning=!0;let e=this.queue.shift();if(!e){this.isRunning=!1;return}e(),this.stack>this.stackSize?setTimeout(()=>{this.isRunning=!1,this.run(),this.stack=0},this.interval):(this.isRunning=!1,this.run())}};var Ir=class n{constructor(e){this.children=[],this.isLeaf=!0,Object.assign(this,e)}static create(e){return new n(e)}};function Na(n,e,t,r){let i=Fa(n,e,r),s=[];return n.forEach(o=>{let a=e(o),u=t(o),c=i.get(a);if(c)if(u){let d=i.get(u);if(!d)return;d.children.push(c),d.isLeaf=!1,c.parent=d}else s.push(c)}),s}function Fa(n,e,t){let r=new Map;return n.forEach(i=>r.set(e(i),t(i))),r}function xa(n,e){if(!Jt(n)||!n.some(r=>!!r.group))return;let t=new Map;for(let r of n){let i=r?.group||e;if(typeof i!="string")throw new Error(`Invalid group: ${i}`);let s=t.get(i)||[];s.push(r),t.set(i,s)}return t}var Bd=new C("LOADER_DELAY");var Kd=new C("LIST_QUERY_DEBOUNCE_TIME");var La=(()=>{class n{constructor(t){this.configState=t}getGrantedPolicy$(t){return this.getStream().pipe(E(r=>this.isPolicyGranted(t,r)))}getGrantedPolicy(t){let r=this.getSnapshot();return this.isPolicyGranted(t,r)}filterItemsByPolicy(t){let r=this.getSnapshot();return t.filter(i=>!i.requiredPolicy||this.isPolicyGranted(i.requiredPolicy,r))}filterItemsByPolicy$(t){return this.getStream().pipe(E(r=>t.filter(i=>!i.requiredPolicy||this.isPolicyGranted(i.requiredPolicy,r))))}isPolicyGranted(t,r){if(!t)return!0;let i=/\|\|/g,s=/&&/g;if(i.test(t)){let o=t.split("||").filter(Boolean);return o.length<2?!1:o.some(a=>this.getPolicy(a.trim(),r))}else if(s.test(t)){let o=t.split("&&").filter(Boolean);return o.length<2?!1:o.every(a=>this.getPolicy(a.trim(),r))}return this.getPolicy(t,r)}getStream(){return this.configState.getAll$().pipe(E(this.mapToPolicies))}getSnapshot(){return this.mapToPolicies(this.configState.getAll())}mapToPolicies(t){return t?.auth?.grantedPolicies||{}}getPolicy(t,r){return r[t]||!1}static{this.\u0275fac=function(r){return new(r||n)(S(ge))}}static{this.\u0275prov=I({token:n,factory:n.\u0275fac,providedIn:"root"})}}return n})();var Aa=new C("COOKIE_LANGUAGE_KEY",{factory:()=>".AspNetCore.Culture"}),Jd=new C("NAVIGATE_TO_MANAGE_PROFILE"),Ra=new C("QUEUE_MANAGER"),ps=new C("INCUDE_LOCALIZATION_RESOURCES_TOKEN"),Qd=new C("PIPE_TO_LOGIN_FN_KEY"),Xd=new C("SET_TOKEN_RESPONSE_TO_STORAGE_FN_KEY"),gs=new C("OTHERS_GROUP"),ys=new C("SORT_COMPARE_FUNC");function $a(){let n=N(ye);return(t,r)=>{let i=t.order,s=r.order;if(i>s)return 1;if(i<s)return-1;if(t.id>r.id)return 1;if(t.id<r.id)return-1;if(!Number.isInteger(i))return 1;if(!Number.isInteger(s))return-1;let o=n.instant(t.name),a=n.instant(r.name);return o>a?1:o<a?-1:0}}var za=new C("DYNAMIC_LAYOUTS"),_a=new C("DISABLE_APP_NAME"),Or=class{constructor(){this._flat$=new B([]),this._tree$=new B([]),this._visible$=new B([]),this.shouldSingularizeRoutes=!0}get flat(){return this._flat$.value}get flat$(){return this._flat$.asObservable()}get tree(){return this._tree$.value}get tree$(){return this._tree$.asObservable()}get visible(){return this._visible$.value}get visible$(){return this._visible$.asObservable()}filterWith(e){return this._flat$.value.filter(t=>!e.has(t[this.id]))}findItemsToRemove(e){return this._flat$.value.reduce((t,r)=>{if(!t.has(r[this.parentId]))return t;let i=new Set([r[this.id]]),s=this.findItemsToRemove(i);return new Set([...t,...s])},e)}publish(e){return this._flat$.next(e),this._tree$.next(this.createTree(e)),this._visible$.next(this.createTree(e.filter(t=>!this.hide(t)))),e}createTree(e){return Na(e,t=>t[this.id],t=>t[this.parentId],t=>Ir.create(t))}createGroupedTree(e){let t=xa(e,this.othersGroup);if(t)return Array.from(t,([r,i])=>({group:r,items:i}))}add(e){let t=[];if(this.shouldSingularizeRoutes||(t=[...this.flat,...e]),this.shouldSingularizeRoutes){let r=new Map;e.forEach(i=>r.set(i[this.id],i)),t=this.filterWith(r),r.forEach(ca(t))}return t.sort(this.sort),this.publish(t)}find(e,t=this.tree){return t.reduce((r,i)=>r||(e(i)?i:this.find(e,i.children)),null)}patch(e,t){let r=this._flat$.value,i=r.findIndex(s=>s[this.id]===e);return i<0?!1:(r[i]=l(l({},r[i]),t),r.sort(this.sort),this.publish(r))}refresh(){return this.add([])}remove(e){let t=new Set;e.forEach(s=>t.add(s));let r=this.findItemsToRemove(t),i=this.filterWith(r);return this.publish(i)}removeByParam(e){if(!e)return null;let t=Object.keys(e);if(t.length===0)return null;let r=this.flat.filter(s=>t.every(o=>s[o]===e[o]));if(!r?.length)return null;for(let s of r)this.removeByParam({[this.parentId]:s[this.id]});let i=this.flat.filter(s=>!r.includes(s));return this.publish(i)}search(e,t=this.tree){let r=Object.keys(e);return t.reduce((i,s)=>i||(r.every(o=>s[o]===e[o])?s:this.search(e,s.children)),null)}setSingularizeStatus(e=!0){this.shouldSingularizeRoutes=e}},ja=(()=>{class n extends Or{constructor(t){super(),this.injector=t,this.id="name",this.parentId="parentName",this.hide=i=>i.invisible||!this.isGranted(i),this.sort=(i,s)=>this.compareFunc(i,s);let r=this.injector.get(ge);this.subscription=r.createOnUpdateStream(i=>i).subscribe(()=>this.refresh()),this.permissionService=t.get(La),this.othersGroup=t.get(gs),this.compareFunc=t.get(ys)}isGranted({requiredPolicy:t}){return this.permissionService.getGrantedPolicy(t)}hasChildren(t){return!!this.find(i=>i[this.id]===t)?.children?.length}hasInvisibleChild(t){return this.find(i=>i[this.id]===t)?.children?.some(i=>i.invisible)||!1}ngOnDestroy(){this.subscription.unsubscribe()}static{this.\u0275fac=function(r){return new(r||n)(S(K))}}static{this.\u0275prov=I({token:n,factory:n.\u0275fac})}}return n})(),Va=(()=>{class n extends ja{hasPathOrChild(t){return!!t.path||this.hasChildren(t.name)}get groupedVisible(){return this.createGroupedTree(this.visible.filter(t=>this.hasPathOrChild(t)))}get groupedVisible$(){return this.visible$.pipe(E(t=>t.filter(r=>this.hasPathOrChild(r))),E(t=>this.createGroupedTree(t)))}static{this.\u0275fac=(()=>{let t;return function(i){return(t||(t=ln(n)))(i||n)}})()}static{this.\u0275prov=I({token:n,factory:n.\u0275fac,providedIn:"root"})}}return n})();var Ua=(()=>{class n{constructor(){this.window=N(Ve).defaultView,this.window.addEventListener("storage",t=>{if(t.key==="access_token"){let r=t.newValue===null,i=t.oldValue===null&&t.newValue!==null;(r||i)&&this.window.location.assign("/")}})}static{this.\u0275fac=function(r){return new(r||n)}}static{this.\u0275prov=I({token:n,factory:n.\u0275fac,providedIn:"root"})}}return n})(),Wa=(()=>{class n extends Tn{constructor(){super(),this.title=N(Vr),this.localizationService=N(ye),this.disableProjectName=N(_a,{optional:!0}),this.langugageChange=In(this.localizationService.languageChange$),xt(()=>{this.langugageChange()&&this.updateTitle(this.routerState)})}updateTitle(t){this.routerState=t;let r=this.buildTitle(t),i=this.localizationService.instant({key:"::AppName",defaultValue:"MyProjectName"});if(!r)return this.title.setTitle(i);let s=this.localizationService.instant({key:r,defaultValue:r});this.disableProjectName||(s+=` | ${i}`),this.title.setTitle(s)}static{this.\u0275fac=function(r){return new(r||n)}}static{this.\u0275prov=I({token:n,factory:n.\u0275fac,providedIn:"root"})}}return n})(),Pa=(()=>{class n{constructor(){this.configState=N(ge),this.document=N(Ve),this.cookieKey="__timezone",this.configState.getOne$("setting").subscribe(t=>{this.timeZoneNameFromSettings=t?.values?.["Abp.Timing.TimeZone"]}),this.configState.getOne$("clock").subscribe(t=>{this.isUtcClockEnabled=t?.kind==="Utc"})}get timezone(){return this.isUtcClockEnabled?this.timeZoneNameFromSettings||this.getBrowserTimezone():this.getBrowserTimezone()}getBrowserTimezone(){return Intl.DateTimeFormat().resolvedOptions().timeZone}setTimezone(t){this.isUtcClockEnabled&&(this.document.cookie=`${this.cookieKey}=${t}; path=/`)}static{this.\u0275fac=function(r){return new(r||n)}}static{this.\u0275prov=I({token:n,factory:n.\u0275fac,providedIn:"root"})}}return n})();var Ha=(()=>{class n{constructor(t){this.restService=t,this.apiName="abp",this.get=(r,i)=>this.restService.request({method:"GET",url:"/api/abp/application-configuration",params:{includeLocalizationResources:r.includeLocalizationResources}},l({apiName:this.apiName},i))}static{this.\u0275fac=function(r){return new(r||n)(S(en))}}static{this.\u0275prov=I({token:n,factory:n.\u0275fac,providedIn:"root"})}}return n})(),Za=(()=>{class n{constructor(t){this.restService=t,this.apiName="abp",this.get=(r,i)=>this.restService.request({method:"GET",url:"/api/abp/application-localization",params:{cultureName:r.cultureName,onlyDynamics:r.onlyDynamics}},l({apiName:this.apiName},i))}static{this.\u0275fac=function(r){return new(r||n)(S(en))}}static{this.\u0275prov=I({token:n,factory:n.\u0275fac,providedIn:"root"})}}return n})(),ge=(()=>{class n{setState(t){this.store.set(t)}get createOnUpdateStream(){return this.store.sliceUpdate}constructor(t,r,i){this.abpConfigService=t,this.abpApplicationLocalizationService=r,this.includeLocalizationResources=i,this.updateSubject=new ze,this.store=new Et({}),this.initUpdateStream()}initUpdateStream(){this.updateSubject.pipe(ee(()=>this.abpConfigService.get({includeLocalizationResources:!!this.includeLocalizationResources}))).pipe(ee(t=>this.getLocalizationAndCombineWithAppState(t))).subscribe(t=>this.store.set(t))}getLocalizationAndCombineWithAppState(t){if(!t.localization.currentCulture.cultureName)throw new Error("culture name should defined");let r=this.uiCultureFromAuthCodeFlow??t.localization.currentCulture.cultureName;return this.getlocalizationResource(r).pipe(E(i=>b(l({},t),{localization:l(l({},t.localization),i)})),Te(()=>this.uiCultureFromAuthCodeFlow=void 0))}getlocalizationResource(t){return this.abpApplicationLocalizationService.get({cultureName:t,onlyDynamics:!1})}refreshAppState(){return this.updateSubject.next(),this.createOnUpdateStream(t=>t).pipe(Ct(1))}refreshLocalization(t){return this.includeLocalizationResources?this.refreshAppState().pipe(E(()=>null)):this.getlocalizationResource(t).pipe(Te(r=>this.store.patch({localization:l(l({},this.store.state.localization),r)}))).pipe(E(()=>null))}getOne$(t){return this.store.sliceState(r=>r[t])}getOne(t){return this.store.state[t]}getAll$(){return this.store.sliceState(t=>t)}getAll(){return this.store.state}getDeep$(t){return t=ss(t),this.store.sliceState(r=>r).pipe(E(r=>t.reduce((i,s)=>{if(i)return i[s]},r)))}getDeep(t){return t=ss(t),t.reduce((r,i)=>{if(r)return r[i]},this.store.state)}getFeature(t){return this.store.state.features?.values?.[t]}getFeature$(t){return this.store.sliceState(r=>r.features?.values?.[t])}getFeatures(t){let{features:r}=this.store.state;if(r)return t.reduce((i,s)=>b(l({},i),{[s]:r.values[s]}),{})}getFeatures$(t){return this.store.sliceState(({features:r})=>{if(r?.values)return t.reduce((i,s)=>b(l({},i),{[s]:r.values[s]}),{})})}isFeatureEnabled(t,r){return r.values[t]==="true"}getFeatureIsEnabled(t){return this.isFeatureEnabled(t,this.store.state.features)}getFeatureIsEnabled$(t){return this.store.sliceState(r=>this.isFeatureEnabled(t,r.features))}getSetting(t){return this.store.state.setting?.values?.[t]}getSetting$(t){return this.store.sliceState(r=>r.setting?.values?.[t])}getSettings(t){let r=this.store.state.setting?.values||{};return t?Object.keys(r).filter(s=>s.indexOf(t)>-1).reduce((s,o)=>(s[o]=r[o],s),{}):r}getSettings$(t){return this.store.sliceState(r=>r.setting?.values).pipe(E((r={})=>t?Object.keys(r).filter(s=>s.indexOf(t)>-1).reduce((s,o)=>(s[o]=r[o],s),{}):r))}getGlobalFeatures(){return this.store.state.globalFeatures}getGlobalFeatures$(){return this.store.sliceState(t=>t.globalFeatures)}isGlobalFeatureEnabled(t,r){return(r.enabledFeatures||[]).some(s=>t===s)}getGlobalFeatureIsEnabled(t){return this.isGlobalFeatureEnabled(t,this.store.state.globalFeatures)}getGlobalFeatureIsEnabled$(t){return this.store.sliceState(r=>this.isGlobalFeatureEnabled(t,r.globalFeatures))}static{this.\u0275fac=function(r){return new(r||n)(S(Ha),S(Za),S(ps,8))}}static{this.\u0275prov=I({token:n,factory:n.\u0275fac,providedIn:"root"})}}return n})();function ss(n){if(typeof n=="string"&&(n=n.split(".")),!Array.isArray(n))throw new Error("The argument must be a dot string or an string array.");return n}var ye=(()=>{class n{get currentLang(){return this.latestLang||this.sessionState.getLanguage()}get currentLang$(){return this.sessionState.getLanguage$()}get languageChange$(){return this._languageChange$.asObservable()}constructor(t,r,i,s){if(this.sessionState=t,this.injector=r,this.configState=s,this.latestLang=this.sessionState.getLanguage(),this._languageChange$=new ze,this.uiLocalizations$=new B(new Map),this.localizations$=new B(new Map),i)throw new Error("LocalizationService should have only one instance.");this.listenToSetLanguage(),this.initLocalizationValues()}initLocalizationValues(){Tr.subscribe(o=>this.addLocalization(o));let t=this.configState.getDeep$("localization.values"),r=this.configState.getDeep$("localization.resources"),i=this.sessionState.getLanguage$(),s=sn([i,this.uiLocalizations$]).pipe(E(([o,a])=>a.get(o)));sn([t,r,s]).pipe(E(([o,a,u])=>{if(!a)return;let c=qa(o||{},a);return c&&(u||(u=new Map),Object.entries(c).forEach(d=>{let h=d[0],g=d[1],m=u?.get(h)||{};m=l(l({},m),g),u?.set(h,m)})),u}),ce(Boolean)).subscribe(o=>this.localizations$.next(o))}addLocalization(t){if(!t)return;let r=this.uiLocalizations$.value;t.forEach(i=>{let s=r.get(i.culture)||new Map;i.resources.forEach(o=>{let a=s.get(o.resourceName)||{};a=l(l({},a),o.texts),s.set(o.resourceName,a)}),r.set(i.culture,s)}),this.uiLocalizations$.next(r)}listenToSetLanguage(){this.sessionState.onLanguageChange$().pipe(ce(t=>this.configState.getDeep("localization.currentCulture.cultureName")!==t),ee(t=>this.configState.refreshLocalization(t).pipe(E(()=>t))),ce(Boolean),ee(t=>Nr(this.registerLocale(t).then(()=>t)))).subscribe(t=>this._languageChange$.next(t))}registerLocale(t){let{registerLocaleFn:r}=this.injector.get(bt);return r(t).then(i=>{i?.default&&pn(i.default),this.latestLang=t})}get(t,...r){return this.configState.getAll$().pipe(E(i=>this.getLocalization(i,t,...r)))}getResource(t){return this.localizations$.value.get(t)}getResource$(t){return this.localizations$.pipe(E(r=>r.get(t)))}instant(t,...r){return this.getLocalization(this.configState.getAll(),t,...r)}localize(t,r,i){return this.configState.getOne$("localization").pipe(E(wr),E(s=>s(t,r,i)))}localizeSync(t,r,i){let s=this.configState.getOne("localization");return wr(s)(t,r,i)}localizeWithFallback(t,r,i){return this.configState.getOne$("localization").pipe(E(es),E(s=>s(t,r,i)))}localizeWithFallbackSync(t,r,i){let s=this.configState.getOne("localization");return es(s)(t,r,i)}getLocalization(t,r,...i){let s="";if(!r)return s;typeof r!="string"&&(s=r.defaultValue,r=r.key);let o=r.split("::"),a=g=>{Rr()&&console.warn(g)};if(o.length<2)return a("The localization source separator (::) not found."),s||r;if(!t.localization)return s||o[1];let u=o[0]||t.localization.defaultResourceName,c=o[1];if(u==="_")return s||c;if(!u)return a("Localization source name is not specified and the defaultResourceName was not defined!"),s||c;let d=this.localizations$.value.get(u);if(!d)return a("Could not find localization source: "+u),s||c;let h=d[c];return typeof h>"u"?s||c:(i=i.filter(g=>g!=null),h&&(h=ua(h,i)),typeof h!="string"&&(h=""),h||s||r)}static{this.\u0275fac=function(r){return new(r||n)(S(Ot),S(K),S(n,12),S(ge))}}static{this.\u0275prov=I({token:n,factory:n.\u0275fac,providedIn:"root"})}}return n})();function vs(n,e){let t=e[n];return t.baseResources.length===0?t:t.baseResources.reduce((r,i)=>{let s=vs(i,e),o=l(l({},s.texts),t.texts);return b(l({},r),{texts:o})},t)}function Ga(n){return Object.keys(n).map(t=>{let r=vs(t,n);return[t,r]}).reduce((t,[r,i])=>b(l({},t),{[r]:i}),{})}function qa(n,e){let t=Ga(e);return Object.entries(t).reduce((r,[i,s])=>b(l({},r),{[i]:s.texts}),n)}var os={aa:"en","aa-DJ":"en","aa-ER":"en","aa-ET":"en","af-ZA":"af","agq-CM":"agq","ak-GH":"ak","am-ET":"am","ar-001":"ar",arn:"en","arn-CL":"en","as-IN":"as","asa-TZ":"asa","ast-ES":"ast","az-Cyrl-AZ":"az-Cyrl","az-Latn-AZ":"az-Latn",ba:"ru","ba-RU":"ru","bas-CM":"bas","be-BY":"be","bem-ZM":"bem","bez-TZ":"bez","bg-BG":"bg",bin:"en","bin-NG":"en","bm-Latn":"bm","bm-Latn-ML":"bm","bn-BD":"bn","bo-CN":"bo","br-FR":"br","brx-IN":"brx","bs-Cyrl-BA":"bs-Cyrl","bs-Latn-BA":"bs-Latn",byn:"en","byn-ER":"en","ca-ES":"ca","ca-ES-valencia":"ca-ES-VALENCIA","ce-RU":"ce","cgg-UG":"cgg","chr-Cher":"chr","chr-Cher-US":"chr",co:"en","co-FR":"fr","cs-CZ":"cs","cu-RU":"cu","cy-GB":"cy","da-DK":"da","dav-KE":"dav","de-DE":"de","dje-NE":"dje","dsb-DE":"dsb","dua-CM":"dua",dv:"en","dv-MV":"en","dyo-SN":"dyo","dz-BT":"dz","ebu-KE":"ebu","ee-GH":"ee","el-GR":"el","en-029":"en","en-ID":"en","en-US":"en","eo-001":"en","es-ES":"es","et-EE":"et","eu-ES":"eu","ewo-CM":"ewo","fa-IR":"fa","ff-Latn-SN":"ff-Latn","ff-NG":"ff","fi-FI":"fi","fil-PH":"fil","fo-FO":"fo","fr-029":"fr","fr-FR":"fr","fur-IT":"fur","fy-NL":"fy","ga-IE":"ga","gd-GB":"gd","gl-ES":"gl",gn:"en","gn-PY":"en","gsw-CH":"gsw","gu-IN":"gu","guz-KE":"guz","gv-IM":"gv","ha-Latn":"ha","ha-Latn-GH":"ha-GH","ha-Latn-NE":"ha-NE","ha-Latn-NG":"ha","haw-US":"haw","he-IL":"he","hi-IN":"hi","hr-HR":"hr","hsb-DE":"hsb","hu-HU":"hu","hy-AM":"hy","ia-001":"ia","ia-FR":"ia",ibb:"en","ibb-NG":"en","id-ID":"id","ig-NG":"ig","ii-CN":"ii","is-IS":"is","it-IT":"it",iu:"en","iu-Cans":"en","iu-Cans-CA":"en","iu-Latn":"en","iu-Latn-CA":"en","ja-JP":"ja","jgo-CM":"jgo","jmc-TZ":"jmc","jv-Java":"jv","jv-Java-ID":"jv","jv-Latn":"jv","jv-Latn-ID":"jv","ka-GE":"ka","kab-DZ":"kab","kam-KE":"kam","kde-TZ":"kde","kea-CV":"kea","khq-ML":"khq","ki-KE":"ki","kk-KZ":"kk","kkj-CM":"kkj","kl-GL":"kl","kln-KE":"kln","km-KH":"km","kn-IN":"kn","ko-KR":"ko","kok-IN":"kok",kr:"en","kr-NG":"en","ks-Arab":"ks","ks-Arab-IN":"ks","ks-Deva":"ks","ks-Deva-IN":"ks","ksb-TZ":"ksb","ksf-CM":"ksf","ksh-DE":"ksh","ku-Arab":"ku","ku-Arab-IQ":"ku","ku-Arab-IR":"ku","kw-GB":"kw","ky-KG":"ky",la:"en","la-001":"en","lag-TZ":"lag","lb-LU":"lb","lg-UG":"lg","lkt-US":"lkt","ln-CD":"ln","lo-LA":"lo","lrc-IR":"lrc","lt-LT":"lt","lu-CD":"lu","luo-KE":"luo","luy-KE":"luy","lv-LV":"lv","mas-KE":"mas","mer-KE":"mer","mfe-MU":"mfe","mg-MG":"mg","mgh-MZ":"mgh","mgo-CM":"mgo","mi-NZ":"mi","mk-MK":"mk","ml-IN":"ml","mn-Cyrl":"mn","mn-MN":"mn","mn-Mong":"mn","mn-Mong-CN":"mn","mn-Mong-MN":"mn",mni:"en","mni-IN":"en",moh:"en","moh-CA":"en","mr-IN":"mr","ms-MY":"ms","mt-MT":"mt","mua-CM":"mua","my-MM":"my","mzn-IR":"mzn","naq-NA":"naq","nb-NO":"nb","nd-ZW":"nd","ne-NP":"ne","nl-NL":"nl","nmg-CM":"ngm","nn-NO":"nn","nnh-CM":"nnh",no:"en",nqo:"en","nqo-GN":"en",nr:"en","nr-ZA":"en",nso:"en","nso-ZA":"en","nus-SS":"nus","nyn-UG":"nyn",oc:"en","oc-FR":"fr","om-ET":"om","or-IN":"or","os-GE":"os","pa-Arab-PK":"pa-Arab","pa-IN":"pa",pap:"en","pap-029":"en","pl-PL":"pl","prg-001":"prg",prs:"en","prs-AF":"en","ps-AF":"ps","pt-BR":"pt",quc:"en","quc-Latn":"en","quc-Latn-GT":"en",quz:"en","quz-BO":"en","quz-EC":"en","quz-PE":"en","rm-CH":"rm","rn-BI":"rn","ro-RO":"ro","rof-TZ":"rof","ru-RU":"ru","rw-RW":"rw","rwk-TZ":"rwk",sa:"en","sa-IN":"en","sah-RU":"sah","saq-KE":"saq","sbp-TZ":"en","sd-Arab":"sd","sd-Arab-PK":"sd","sd-Deva":"sd","sd-Deva-IN":"sd","se-NO":"se","seh-MZ":"seh","ses-ML":"ses","sg-CF":"sg","shi-Latn-MA":"shi-Latn","shi-Tfng-MA":"shi-Tfng","si-LK":"si","sk-SK":"sk","sl-SI":"sl",sma:"en","sma-NO":"en","sma-SE":"en",smj:"en","smj-NO":"en","smj-SE":"en","smn-FI":"en",sms:"en","sms-FI":"en","sn-Latn":"sn","sn-Latn-ZW":"sn","so-SO":"so","sq-AL":"so","sr-Cyrl-RS":"sr-Cryl","sr-Latn-RS":"sr-Latn",ss:"en","ss-SZ":"en","ss-ZA":"en",ssy:"en","ssy-ER":"en",st:"en","st-LS":"en","st-ZA":"en","sv-SE":"sv","sw-TZ":"sw",syr:"en","syr-SY":"en","ta-IN":"ta","te-IN":"te","teo-UG":"teo","tg-Cyrl":"tg","tg-Cyrl-TJ":"tg","th-TH":"th","ti-ET":"ti",tig:"en","tig-ER":"en","tk-TM":"tk",tn:"en","tn-BW":"en","tn-ZA":"en","to-TO":"to","tr-TR":"tr",ts:"en","ts-ZA":"en","tt-RU":"tt","twq-NE":"twq","tzm-Arab":"tzm","tzm-Arab-MA":"tzm","tzm-Latn":"tzm","tzm-Latn-DZ":"tzm","tzm-Latn-MA":"tzm","tzm-Tfng":"tzm","tzm-Tfng-MA":"tzm","ug-CN":"ug","uk-UA":"uk","ur-PK":"ur","uz-Arab-AF":"uz-Arab","uz-Cyrl-UZ":"uz-Cyrl","uz-Latn-UZ":"uz-Latn","vai-Latn-LR":"vai-Latn","vai-Vaii-LR":"vai-Vaii",ve:"en","ve-ZA":"en","vi-VN":"vi","vo-001":"vo","vun-TZ":"vun","wae-CH":"wae",wal:"en","wal-ET":"en","wo-SN":"wo","xh-ZA":"xh","xog-UG":"xog","yav-CM":"yav","yi-001":"yi","yo-NG":"yo","zgh-Tfng":"zgh","zgh-Tfng-MA":"zgh","zh-CN":"zh","zh-HK":"zh","zh-MO":"zh","zh-SG":"zh","zh-TW":"zh","zu-ZA":"zu"},Ya=new Map([["application","Theme.ApplicationLayoutComponent"],["account","Theme.AccountLayoutComponent"],["empty","Theme.EmptyLayoutComponent"]]);var Ba=(()=>{class n{constructor(t){this.localization=t}transform(t="",...r){let i=r.reduce((s,o)=>s?o?Array.isArray(o)?[...s,...o]:[...s,o]:s:o,[])||[];return this.localization.instant(t,...i)}static{this.\u0275fac=function(r){return new(r||n)(xr(ye,16))}}static{this.\u0275pipe=Lr({name:"abpLocalization",type:n,pure:!0,standalone:!1})}static{this.\u0275prov=I({token:n,factory:n.\u0275fac})}}return n})();var ef=new C("INJECTOR_PIPE_DATA_TOKEN");var Qt=(()=>{class n{static{this.\u0275fac=function(r){return new(r||n)}}static{this.\u0275mod=je({type:n})}static{this.\u0275inj=_e({})}}return n})();Date.prototype.toLocalISOString=function(){let n=this.getTimezoneOffset();return new Date(this.getTime()-n*6e4).toISOString()};function Ka(){let n=N(K),e=n.get(Ot),t=n.get(Ve),r=n.get(Aa);e.getLanguage$().subscribe(i=>{let s=encodeURIComponent(`c=${i}|uic=${i}`);t.cookie=`${r}=${s}`})}var Ja=dn(()=>{Ka()}),kr=class extends String{constructor(e){super(),this.localizationService=e}toString(){let{currentLang:e}=this.localizationService;return ha(os,e)?os[e]:e}valueOf(){return this.toString()}},Qa={provide:Ar,useClass:kr,deps:[ye]},Xa={provide:ps,useValue:!1},eu=(()=>{class n{constructor(t,r){this.routes=t,this.router=r,this.addRoutes()}addRoutes(){this.router?.config?.forEach(({path:t="",data:r})=>{let i=r?.routes;if(i)if(Array.isArray(i))this.routes.add(i);else{let s=Ts([l({path:t},i)],{path:""});this.routes.add(s)}})}static{this.\u0275fac=function(r){return new(r||n)(S(Va),S(wn,8))}}static{this.\u0275prov=I({token:n,factory:n.\u0275fac,providedIn:"root"})}}return n})();function Ts(n,e){return n?n.reduce((t,r)=>{let o=b(l({},r),{parentName:e.name,path:(e.path+"/"+r.path).replace(/\/\//g,"/")}),{children:i}=o,s=ue(o,["children"]);return t.push(s,...Ts(i,s)),t},[]):[]}var tu=(()=>{class n{constructor(){this.timezoneService=N(Pa)}intercept(t,r){if(!this.timezoneService.isUtcClockEnabled)return r.handle(t);let i=this.timezoneService.timezone;return i&&(t=t.clone({setHeaders:{__timezone:i}})),r.handle(t)}static{this.\u0275fac=function(r){return new(r||n)}}static{this.\u0275prov=I({token:n,factory:n.\u0275fac,providedIn:"root"})}}return n})(),ws=function(n){return n[n.Options=0]="Options",n[n.CompareFunctionFactory=1]="CompareFunctionFactory",n[n.TitleStrategy=2]="TitleStrategy",n}(ws||{});function nu(n,e){return{\u0275kind:n,\u0275providers:e}}function ru(n={}){return nu(ws.Options,[{provide:"CORE_OPTIONS",useValue:n},{provide:bt,useFactory:sa,deps:["CORE_OPTIONS"]},{provide:ms,useValue:n.tenantKey||"__tenant"},{provide:us,multi:!0,useValue:cs(n.localizations),deps:[ye]},{provide:gs,useValue:n.othersGroup||"AbpUi::OthersGroup"},{provide:za,useValue:n.dynamicLayouts||Ya}])}function iu(...n){let e=[Lt(yn(),vn({cookieName:"XSRF-TOKEN",headerName:"RequestVerificationToken"})),dn(()=>{Ma(),Ca(),N(ye),N(Ua),N(eu)}),Qa,Ja,{provide:ys,useFactory:$a},{provide:Ra,useClass:br},vr,Xa,{provide:Tn,useExisting:Wa},{provide:jr,useClass:tu,multi:!0}];for(let t of n)e.push(...t.\u0275providers);return un(e)}function su(n={}){return un([{provide:us,multi:!0,useValue:cs(n.localizations),deps:[ye]}])}var Xt=(()=>{class n{static{this.\u0275fac=function(r){return new(r||n)}}static{this.\u0275mod=je({type:n})}static{this.\u0275inj=_e({providers:[Ba,Lt(yn())],imports:[mn,En,bn,Sn,Qt,mn,En,bn,Sn,Qt]})}}return n})(),as=(()=>{class n{static{this.\u0275fac=function(r){return new(r||n)}}static{this.\u0275mod=je({type:n})}static{this.\u0275inj=_e({providers:[Lt(vn({cookieName:"XSRF-TOKEN",headerName:"RequestVerificationToken"}))],imports:[Xt,Qt,Xt,Qt]})}}return n})(),tf=(()=>{class n{static forRoot(t={}){return{ngModule:as,providers:[iu(ru(t))]}}static forChild(t={}){return{ngModule:as,providers:[su(t)]}}static{this.\u0275fac=function(r){return new(r||n)}}static{this.\u0275mod=je({type:n})}static{this.\u0275inj=_e({imports:[Xt,Xt]})}}return n})();export{Ds as a,Yd as b,en as c,tf as d};
