import{a as Ve}from"./chunk-OLNEY35K.js";import{a as ye}from"./chunk-VBHJ4LBO.js";import{b as Ce,c as ie,f as Ie,g as we,h as De,j as ke,k as Ee,l as Me}from"./chunk-22JWGO27.js";import{N as ue,V as ee,a as A,ba as fe,ca as z,da as me,ea as xe,ga as Ne,ja as te,la as Te,ma as be,p as Z,q as he,r as _e,ra as ve,sa as Se,ua as ne}from"./chunk-SXMRENJM.js";import{c as j,d as W,e as G,f as Y,g as J,k as X}from"./chunk-GDGXRFMB.js";import{Ab as u,Bb as C,Cb as v,Db as S,Eb as M,Fb as k,Kb as I,Lb as d,Qb as y,Rb as $,S as oe,Sb as x,T as re,Ta as s,Tb as N,U as le,Vb as q,Wb as ce,Xb as pe,Ya as de,Z as R,ac as ge,cb as Q,cc as D,db as se,dc as K,ea as ae,fa as g,ga as h,gb as B,ia as P,ib as c,oa as T,oc as O,pb as w,qb as a,rb as L,tb as U,ub as F,vb as H,xc as m,yc as E,zb as _}from"./chunk-YUW2MUHJ.js";import{a as V}from"./chunk-EQDQRRRY.js";var ze=t=>({height:t}),Le=t=>({"p-tree-node-droppoint-active":t}),Re=(t,l)=>({$implicit:t,loading:l}),Pe=(t,l)=>({$implicit:t,partialSelected:l,class:"p-tree-node-checkbox"}),Ue=t=>({$implicit:t});function Qe(t,l){if(t&1){let e=k();_(0,"li",11),I("drop",function(n){g(e);let o=d(2);return h(o.onDropPoint(n,-1))})("dragover",function(n){g(e);let o=d(2);return h(o.onDropPointDragOver(n))})("dragenter",function(n){g(e);let o=d(2);return h(o.onDropPointDragEnter(n,-1))})("dragleave",function(n){g(e);let o=d(2);return h(o.onDropPointDragLeave(n))}),u()}if(t&2){let e=d(2);a("ngClass",D(2,Le,e.draghoverPrev)),w("aria-hidden",!0)}}function Be(t,l){t&1&&C(0,"ChevronRightIcon",13),t&2&&a("styleClass","p-tree-node-toggle-icon")}function He(t,l){t&1&&C(0,"ChevronDownIcon",13),t&2&&a("styleClass","p-tree-node-toggle-icon")}function qe(t,l){if(t&1&&(v(0),c(1,Be,1,1,"ChevronRightIcon",12)(2,He,1,1,"ChevronDownIcon",12),S()),t&2){let e=d(3);s(),a("ngIf",!e.node.expanded),s(),a("ngIf",e.node.expanded)}}function Ke(t,l){t&1&&(v(0),C(1,"SpinnerIcon",13),S()),t&2&&(s(),a("styleClass","pi-spin p-tree-node-toggle-icon"))}function je(t,l){if(t&1&&(v(0),c(1,qe,3,2,"ng-container",5)(2,Ke,2,1,"ng-container",5),S()),t&2){let e=d(2);s(),a("ngIf",!e.node.loading),s(),a("ngIf",e.loadingMode==="icon"&&e.node.loading)}}function We(t,l){}function Ge(t,l){t&1&&c(0,We,0,0,"ng-template")}function Ye(t,l){if(t&1&&(_(0,"span",14),c(1,Ge,1,0,null,15),u()),t&2){let e=d(2);s(),a("ngTemplateOutlet",e.tree.togglerIconTemplate||e.tree._togglerIconTemplate)("ngTemplateOutletContext",K(2,Re,e.node.expanded,e.node.loading))}}function Je(t,l){}function Xe(t,l){t&1&&c(0,Je,0,0,"ng-template")}function Ze(t,l){if(t&1&&c(0,Xe,1,0,null,15),t&2){let e=d(4);a("ngTemplateOutlet",e.tree.checkboxIconTemplate||e.tree._checkboxIconTemplate)("ngTemplateOutletContext",K(2,Pe,e.isSelected(),e.node.partialSelected))}}function et(t,l){t&1&&(v(0),c(1,Ze,1,5,"ng-template",null,0,O),S())}function tt(t,l){if(t&1){let e=k();_(0,"p-checkbox",16),I("click",function(n){return g(e),h(n.preventDefault())}),c(1,et,3,0,"ng-container",5),u()}if(t&2){let e=d(2);a("ngModel",e.isSelected())("binary",!0)("indeterminate",e.node.partialSelected)("disabled",e.node.selectable===!1)("variant",(e.tree==null?null:e.tree.config.inputStyle())==="filled"||(e.tree==null?null:e.tree.config.inputVariant())==="filled"?"filled":"outlined")("tabindex",-1),w("data-p-partialchecked",e.node.partialSelected),s(),a("ngIf",e.tree.checkboxIconTemplate||e.tree._checkboxIconTemplate)}}function it(t,l){if(t&1&&C(0,"span"),t&2){let e=d(2);F(e.getIcon())}}function nt(t,l){if(t&1&&(_(0,"span"),q(1),u()),t&2){let e=d(2);s(),ce(e.node.label)}}function ot(t,l){t&1&&M(0)}function rt(t,l){if(t&1&&(_(0,"span"),c(1,ot,1,0,"ng-container",15),u()),t&2){let e=d(2);s(),a("ngTemplateOutlet",e.tree.getTemplateForNode(e.node))("ngTemplateOutletContext",D(2,Ue,e.node))}}function lt(t,l){if(t&1&&C(0,"p-treeNode",19),t&2){let e=l.$implicit,i=l.first,n=l.last,o=l.index,r=d(3);a("node",e)("parentNode",r.node)("firstChild",i)("lastChild",n)("index",o)("itemSize",r.itemSize)("level",r.level+1)("loadingMode",r.loadingMode)}}function at(t,l){if(t&1&&(_(0,"ul",17),c(1,lt,1,8,"p-treeNode",18),u()),t&2){let e=d(2);L("display",e.node.expanded?"flex":"none"),s(),a("ngForOf",e.node.children)("ngForTrackBy",e.tree.trackBy.bind(e))}}function dt(t,l){if(t&1){let e=k();_(0,"li",11),I("drop",function(n){g(e);let o=d(2);return h(o.onDropPoint(n,1))})("dragover",function(n){g(e);let o=d(2);return h(o.onDropPointDragOver(n))})("dragenter",function(n){g(e);let o=d(2);return h(o.onDropPointDragEnter(n,1))})("dragleave",function(n){g(e);let o=d(2);return h(o.onDropPointDragLeave(n))}),u()}if(t&2){let e=d(2);a("ngClass",D(2,Le,e.draghoverNext)),w("aria-hidden",!0)}}function st(t,l){if(t&1){let e=k();c(0,Qe,1,4,"li",1),_(1,"li",2),I("keydown",function(n){g(e);let o=d();return h(o.onKeyDown(n))}),_(2,"div",3),I("click",function(n){g(e);let o=d();return h(o.onNodeClick(n))})("contextmenu",function(n){g(e);let o=d();return h(o.onNodeRightClick(n))})("dblclick",function(n){g(e);let o=d();return h(o.onNodeDblClick(n))})("touchend",function(){g(e);let n=d();return h(n.onNodeTouchEnd())})("drop",function(n){g(e);let o=d();return h(o.onDropNode(n))})("dragover",function(n){g(e);let o=d();return h(o.onDropNodeDragOver(n))})("dragenter",function(n){g(e);let o=d();return h(o.onDropNodeDragEnter(n))})("dragleave",function(n){g(e);let o=d();return h(o.onDropNodeDragLeave(n))})("dragstart",function(n){g(e);let o=d();return h(o.onDragStart(n))})("dragend",function(n){g(e);let o=d();return h(o.onDragStop(n))}),_(3,"button",4),I("click",function(n){g(e);let o=d();return h(o.toggle(n))}),c(4,je,3,2,"ng-container",5)(5,Ye,2,5,"span",6),u(),c(6,tt,2,8,"p-checkbox",7)(7,it,1,2,"span",8),_(8,"span",9),c(9,nt,2,1,"span",5)(10,rt,2,4,"span",5),u()(),c(11,at,2,4,"ul",10),u(),c(12,dt,1,4,"li",1)}if(t&2){let e=d();a("ngIf",e.tree.droppableNodes),s(),U(e.node.style),F(e.node.styleClass),a("ngClass",e.nodeClass)("ngStyle",D(29,ze,e.itemSize+"px")),w("aria-label",e.node.label)("aria-checked",e.checked)("aria-setsize",e.node.children?e.node.children.length:0)("aria-selected",e.selected)("aria-expanded",e.node.expanded)("aria-posinset",e.index+1)("aria-level",e.level+1)("tabindex",e.index===0?0:-1)("data-id",e.node.key),s(),L("padding-left",e.level*e.indentation+"rem"),a("ngClass",e.nodeContentClass)("draggable",e.tree.draggableNodes),s(),w("data-pc-section","toggler"),s(),a("ngIf",!e.tree.togglerIconTemplate&&!e.tree._togglerIconTemplate),s(),a("ngIf",e.tree.togglerIconTemplate||e.tree._togglerIconTemplate),s(),a("ngIf",e.tree.selectionMode=="checkbox"),s(),a("ngIf",e.node.icon||e.node.expandedIcon||e.node.collapsedIcon),s(2),a("ngIf",!e.tree.getTemplateForNode(e.node)),s(),a("ngIf",e.tree.getTemplateForNode(e.node)),s(),a("ngIf",!e.tree.virtualScroll&&e.node.children&&e.node.expanded),s(),a("ngIf",e.tree.droppableNodes&&e.lastChild)}}var Fe=["filter"],ct=["node"],pt=["header"],gt=["footer"],ht=["loader"],_t=["empty"],ut=["togglericon"],ft=["checkboxicon"],mt=["loadingicon"],xt=["filtericon"],Nt=["scroller"],Tt=["wrapper"],yt=t=>({options:t});function Ct(t,l){if(t&1&&C(0,"i"),t&2){let e=d(2);F("p-tree-loading-icon pi-spin "+e.loadingIcon)}}function bt(t,l){t&1&&C(0,"SpinnerIcon",16),t&2&&a("spin",!0)("styleClass","p-tree-loading-icon")}function vt(t,l){}function St(t,l){t&1&&c(0,vt,0,0,"ng-template")}function It(t,l){if(t&1&&(_(0,"span",17),c(1,St,1,0,null,9),u()),t&2){let e=d(3);s(),a("ngTemplateOutlet",e.loadingIconTemplate||e._loadingIconTemplate)}}function wt(t,l){if(t&1&&(v(0),c(1,bt,1,2,"SpinnerIcon",14)(2,It,2,1,"span",15),S()),t&2){let e=d(2);s(),a("ngIf",!e.loadingIconTemplate&&!e._loadingIconTemplate),s(),a("ngIf",e.loadingIconTemplate||e._loadingIconTemplate)}}function Dt(t,l){if(t&1&&(_(0,"div",12),c(1,Ct,1,2,"i",13)(2,wt,3,2,"ng-container",10),u()),t&2){let e=d();s(),a("ngIf",e.loadingIcon),s(),a("ngIf",!e.loadingIcon)}}function kt(t,l){t&1&&M(0)}function Et(t,l){t&1&&M(0)}function Mt(t,l){if(t&1&&c(0,Et,1,0,"ng-container",18),t&2){let e=d();a("ngTemplateOutlet",e.filterTemplate||e._filterTemplate)("ngTemplateOutletContext",D(2,Ue,e.filterOptions))}}function Vt(t,l){t&1&&C(0,"SearchIcon",21)}function Ft(t,l){}function Ot(t,l){t&1&&c(0,Ft,0,0,"ng-template")}function zt(t,l){if(t&1&&(_(0,"span"),c(1,Ot,1,0,null,9),u()),t&2){let e=d(3);s(),a("ngTemplateOutlet",e.filterIconTemplate||e._filterIconTemplate)}}function Lt(t,l){if(t&1){let e=k();_(0,"p-iconField")(1,"input",19,0),I("keydown.enter",function(n){return g(e),h(n.preventDefault())})("input",function(n){g(e);let o=d(2);return h(o._filter(n.target.value))}),u(),_(3,"p-inputIcon"),c(4,Vt,1,0,"SearchIcon",20)(5,zt,2,1,"span",10),u()()}if(t&2){let e=d(2);s(),a("pAutoFocus",e.filterInputAutoFocus),w("placeholder",e.filterPlaceholder),s(3),a("ngIf",!e.filterIconTemplate&&!e._filterIconTemplate),s(),a("ngIf",e.filterIconTemplate||e._filterIconTemplate)}}function Ut(t,l){if(t&1&&c(0,Lt,6,4,"p-iconField",10),t&2){let e=d();a("ngIf",e.filter)}}function $t(t,l){if(t&1&&C(0,"p-treeNode",27,3),t&2){let e=l.$implicit,i=l.first,n=l.last,o=l.index,r=d(2).options,p=d(3);a("level",e.level)("rowNode",e)("node",e.node)("parentNode",e.parent)("firstChild",i)("lastChild",n)("index",p.getIndex(r,o))("itemSize",r.itemSize)("indentation",p.indentation)("loadingMode",p.loadingMode)}}function At(t,l){if(t&1&&(_(0,"ul",25),c(1,$t,2,10,"p-treeNode",26),u()),t&2){let e=d(),i=e.$implicit,n=e.options,o=d(3);U(n.contentStyle),a("ngClass",n.contentStyleClass),w("aria-label",o.ariaLabel)("aria-labelledby",o.ariaLabelledBy),s(),a("ngForOf",i)("ngForTrackBy",o.trackBy)}}function Rt(t,l){if(t&1&&c(0,At,2,7,"ul",24),t&2){let e=l.$implicit;a("ngIf",e)}}function Pt(t,l){t&1&&M(0)}function Qt(t,l){if(t&1&&c(0,Pt,1,0,"ng-container",18),t&2){let e=l.options,i=d(4);a("ngTemplateOutlet",i.loaderTemplate||i._loaderTemplate)("ngTemplateOutletContext",D(2,yt,e))}}function Bt(t,l){t&1&&(v(0),c(1,Qt,1,4,"ng-template",null,4,O),S())}function Ht(t,l){if(t&1){let e=k();_(0,"p-scroller",23,1),I("onScroll",function(n){g(e);let o=d(2);return h(o.onScroll.emit(n))})("onScrollIndexChange",function(n){g(e);let o=d(2);return h(o.onScrollIndexChange.emit(n))})("onLazyLoad",function(n){g(e);let o=d(2);return h(o.onLazyLoad.emit(n))}),c(2,Rt,1,1,"ng-template",null,2,O)(4,Bt,3,0,"ng-container",10),u()}if(t&2){let e=d(2);U(D(9,ze,e.scrollHeight!=="flex"?e.scrollHeight:void 0)),a("items",e.serializedValue)("tabindex",-1)("scrollHeight",e.scrollHeight!=="flex"?void 0:"100%")("itemSize",e.virtualScrollItemSize||e._virtualNodeHeight)("lazy",e.lazy)("options",e.virtualScrollOptions),s(4),a("ngIf",e.loaderTemplate||e._loaderTemplate)}}function qt(t,l){if(t&1&&C(0,"p-treeNode",32),t&2){let e=l.$implicit,i=l.first,n=l.last,o=l.index,r=d(4);a("node",e)("firstChild",i)("lastChild",n)("index",o)("level",0)("loadingMode",r.loadingMode)}}function Kt(t,l){if(t&1&&(_(0,"ul",30),c(1,qt,1,6,"p-treeNode",31),u()),t&2){let e=d(3);w("aria-label",e.ariaLabel)("aria-labelledby",e.ariaLabelledBy),s(),a("ngForOf",e.getRootNode())("ngForTrackBy",e.trackBy.bind(e))}}function jt(t,l){if(t&1&&(v(0),_(1,"div",28,5),c(3,Kt,2,4,"ul",29),u(),S()),t&2){let e=d(2);s(),L("max-height",e.scrollHeight),s(2),a("ngIf",e.getRootNode())}}function Wt(t,l){if(t&1&&(v(0),c(1,Ht,5,11,"p-scroller",22)(2,jt,4,3,"ng-container",10),S()),t&2){let e=d();s(),a("ngIf",e.virtualScroll),s(),a("ngIf",!e.virtualScroll)}}function Gt(t,l){if(t&1&&(v(0),q(1),S()),t&2){let e=d(2);s(),pe(" ",e.emptyMessageLabel," ")}}function Yt(t,l){}function Jt(t,l){t&1&&c(0,Yt,0,0,"ng-template",null,6,O)}function Xt(t,l){if(t&1&&(_(0,"div",33),c(1,Gt,2,1,"ng-container",34)(2,Jt,2,0,null,9),u()),t&2){let e=d();s(),a("ngIf",!e.emptyMessageTemplate&&!e._emptyMessageTemplate)("ngIfElse",e.emptyFilter),s(),a("ngTemplateOutlet",e.emptyMessageTemplate||e._emptyMessageTemplate)}}function Zt(t,l){t&1&&M(0)}var ei=({dt:t})=>`
.p-tree {
    background: ${t("tree.background")};
    color: ${t("tree.color")};
    padding: ${t("tree.padding")};
}

.p-tree-root-children,
.p-tree-node-children {
    display: flex;
    list-style-type: none;
    flex-direction: column;
    margin: 0;
    gap: ${t("tree.gap")};
}

.p-tree-root-children {
    padding: 0;
    padding-block-start: ${t("tree.gap")};
}

.p-tree-node-children {
    padding-block-start: ${t("tree.gap")};
    padding-inline-start: ${t("tree.indent")};
}

.p-tree-node {
    padding: 0;
    outline: 0 none;
}

.p-tree-node-content {
    border-radius: ${t("tree.node.border.radius")};
    padding: ${t("tree.node.padding")};
    display: flex;
    align-items: center;
    outline-color: transparent;
    color: ${t("tree.node.color")};
    gap: ${t("tree.node.gap")};
    transition: background ${t("tree.transition.duration")}, color ${t("tree.transition.duration")}, outline-color ${t("tree.transition.duration")}, box-shadow ${t("tree.transition.duration")};
}

.p-tree-node:focus-visible > .p-tree-node-content {
    box-shadow: ${t("tree.node.focus.ring.shadow")};
    outline: ${t("tree.node.focus.ring.width")} ${t("tree.node.focus.ring.style")} ${t("tree.node.focus.ring.color")};
    outline-offset: ${t("tree.node.focus.ring.offset")};
}

.p-tree-node-content.p-tree-node-selectable:not(.p-tree-node-selected):hover {
    background: ${t("tree.node.hover.background")};
    color: ${t("tree.node.hover.color")};
}

.p-tree-node-content.p-tree-node-selectable:not(.p-tree-node-selected):hover .p-tree-node-icon {
    color: ${t("tree.node.icon.hover.color")};
}

.p-tree-node-content.p-tree-node-selected {
    background: ${t("tree.node.selected.background")};
    color: ${t("tree.node.selected.color")};
}

.p-tree-node-content.p-tree-node-selected .p-tree-node-toggle-button {
    color: inherit;
}

.p-tree-node-toggle-button {
    cursor: pointer;
    user-select: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    position: relative;
    flex-shrink: 0;
    width: ${t("tree.node.toggle.button.size")};
    height: ${t("tree.node.toggle.button.size")};
    color: ${t("tree.node.toggle.button.color")};
    border: 0 none;
    background: transparent;
    border-radius: ${t("tree.node.toggle.button.border.radius")};
    transition: background ${t("tree.transition.duration")}, color ${t("tree.transition.duration")}, border-color ${t("tree.transition.duration")}, outline-color ${t("tree.transition.duration")}, box-shadow ${t("tree.transition.duration")};
    outline-color: transparent;
    padding: 0;
}

.p-tree-node-toggle-button:enabled:hover {
    background: ${t("tree.node.toggle.button.hover.background")};
    color: ${t("tree.node.toggle.button.hover.color")};
}

.p-tree-node-content.p-tree-node-selected .p-tree-node-toggle-button:hover {
    background: ${t("tree.node.toggle.button.selected.hover.background")};
    color: ${t("tree.node.toggle.button.selected.hover.color")};
}

.p-tree-root {
    overflow: auto;
}

.p-tree-node-selectable {
    cursor: pointer;
    user-select: none;
}

.p-tree-node-leaf > .p-tree-node-content .p-tree-node-toggle-button {
    visibility: hidden;
}

.p-tree-node-icon {
    color: ${t("tree.node.icon.color")};
    transition: color ${t("tree.transition.duration")};
}

.p-tree-node-content.p-tree-node-selected .p-tree-node-icon {
    color: ${t("tree.node.icon.selected.color")};
}

.p-tree-filter-input {
    width: 100%;
}

.p-tree-loading {
    position: relative;
    height: 100%;
}

.p-tree-loading-icon {
    font-size: ${t("tree.loading.icon.size")};
    width: ${t("tree.loading.icon.size")};
    height: ${t("tree.loading.icon.size")};
}

.p-tree .p-tree-mask {
    position: absolute;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

.p-tree-flex-scrollable {
    display: flex;
    flex: 1;
    height: 100%;
    flex-direction: column;
}

.p-tree-flex-scrollable .p-tree-root {
    flex: 1;
}

/* For PrimeNG */
.p-tree .p-tree-node-droppoint {
    height: 4px;
    list-style-type: none;
}

.p-tree .p-tree-node-droppoint-active {
    border: 0 none;
    background-color: ${t("primary.color")};
}

.p-tree-node-content.p-tree-node-dragover {
    background: ${t("tree.node.hover.background")};
    color: ${t("tree.node.hover.color")};
}

.p-tree-node-content.p-tree-node-dragover .p-tree-node-icon {
    color: ${t("tree.node.icon.hover.color")};
}

.p-tree-horizontal {
    width: auto;
    padding-inline-start: 0;
    padding-inline-end: 0;
    overflow: auto;
}

.p-tree.p-tree-horizontal table,
.p-tree.p-tree-horizontal tr,
.p-tree.p-tree-horizontal td {
    border-collapse: collapse;
    margin: 0;
    padding: 0;
    vertical-align: middle;
}

.p-tree-horizontal .p-tree-node-content {
    font-weight: normal;
    padding: 0.4em 1em 0.4em 0.2em;
    display: flex;
    align-items: center;
}

.p-tree-horizontal .p-tree-node-parent .p-tree-node-content {
    font-weight: normal;
    white-space: nowrap;
}

.p-tree.p-tree-horizontal .p-tree-node.p-tree-node-leaf,
.p-tree.p-tree-horizontal .p-tree-node.p-tree-node-collapsed {
    padding-inline-end: 0;
}

.p-tree.p-tree-horizontal .p-tree-node-children {
    padding: 0;
    margin: 0;
}

.p-tree.p-tree-horizontal .p-tree-node-connector {
    width: 1px;
}

.p-tree.p-tree-horizontal .p-tree-node-connector-table {
    height: 100%;
    width: 1px;
}

.p-tree.p-tree-horizontal table {
    height: 0;
}
`,ti={root:({instance:t})=>({"p-tree p-component":!0,"p-tree-selectable":t.selectionMode!=null,"p-tree-loading":t.loading,"p-tree-flex-scrollable":t.scrollHeight==="flex","p-tree-node-dragover":t.dragHover}),mask:"p-tree-mask p-overlay-mask",loadingIcon:"p-tree-loading-icon",pcFilterInput:"p-tree-filter-input",wrapper:"p-tree-root",rootChildren:"p-tree-root-children",node:({instance:t})=>({"p-tree-node":!0,"p-tree-node-leaf":t.isLeaf()}),nodeContent:({instance:t})=>({"p-tree-node-content":!0,[t.styleClass]:!!t.styleClass,"p-tree-node-selectable":t.selectable,"p-tree-node-dragover":t.draghoverNode,"p-tree-node-selected":t.selectionMode==="checkbox"&&t.tree.highlightOnSelect?t.checked:t.selected}),nodeToggleButton:"p-tree-node-toggle-button",nodeToggleIcon:"p-tree-node-toggle-icon",nodeCheckbox:"p-tree-node-checkbox",nodeIcon:"p-tree-node-icon",nodeLabel:"p-tree-node-label",nodeChildren:"p-tree-node-children"},Oe=(()=>{class t extends Ne{name="tree";theme=ei;classes=ti;static \u0275fac=(()=>{let e;return function(n){return(e||(e=P(t)))(n||t)}})();static \u0275prov=re({token:t,factory:t.\u0275fac})}return t})();var ii=(()=>{class t extends te{static ICON_CLASS="p-tree-node-icon ";rowNode;node;parentNode;root;index;firstChild;lastChild;level;indentation;itemSize;loadingMode;tree=R(oe(()=>$e));timeout;draghoverPrev;draghoverNext;draghoverNode;get selected(){return this.tree.selectionMode==="single"||this.tree.selectionMode==="multiple"?this.isSelected():void 0}get checked(){return this.tree.selectionMode==="checkbox"?this.isSelected():void 0}get nodeClass(){return this.tree._componentStyle.classes.node({instance:this})}get nodeContentClass(){return this.tree._componentStyle.classes.nodeContent({instance:this})}get selectable(){return this.node.selectable===!1?!1:this.tree.selectionMode!=null}ngOnInit(){super.ngOnInit(),this.node.parent=this.parentNode;let i=this.tree.el.nativeElement.closest("p-dialog");this.parentNode&&!i&&(this.setAllNodesTabIndexes(),this.tree.syncNodeOption(this.node,this.tree.value,"parent",this.tree.getNodeWithKey(this.parentNode.key,this.tree.value)))}getIcon(){let e;return this.node.icon?e=this.node.icon:e=this.node.expanded&&this.node.children&&this.node.children?.length?this.node.expandedIcon:this.node.collapsedIcon,t.ICON_CLASS+" "+e+" p-tree-node-icon"}isLeaf(){return this.tree.isNodeLeaf(this.node)}toggle(e){this.node.expanded?this.collapse(e):this.expand(e),e.stopPropagation()}expand(e){this.node.expanded=!0,this.tree.virtualScroll&&(this.tree.updateSerializedValue(),this.focusVirtualNode()),this.tree.onNodeExpand.emit({originalEvent:e,node:this.node})}collapse(e){this.node.expanded=!1,this.tree.virtualScroll&&(this.tree.updateSerializedValue(),this.focusVirtualNode()),this.tree.onNodeCollapse.emit({originalEvent:e,node:this.node})}onNodeClick(e){this.tree.onNodeClick(e,this.node)}onNodeKeydown(e){e.key==="Enter"&&this.tree.onNodeClick(e,this.node)}onNodeTouchEnd(){this.tree.onNodeTouchEnd()}onNodeRightClick(e){this.tree.onNodeRightClick(e,this.node)}onNodeDblClick(e){this.tree.onNodeDblClick(e,this.node)}isSelected(){return this.tree.isSelected(this.node)}isSameNode(e){return e.currentTarget&&(e.currentTarget.isSameNode(e.target)||e.currentTarget.isSameNode(e.target.closest('[role="treeitem"]')))}onDropPoint(e,i){e.preventDefault();let n=this.tree.dragNode,o=this.tree.dragNodeIndex,r=this.tree.dragNodeScope,p=this.tree.dragNodeTree===this.tree?i===1||o!==this.index-1:!0;if(this.tree.allowDrop(n,this.node,r)&&p){let f=V({},this.createDropPointEventMetadata(i));this.tree.validateDrop?this.tree.onNodeDrop.emit({originalEvent:e,dragNode:n,dropNode:this.node,index:this.index,accept:()=>{this.processPointDrop(f)}}):(this.processPointDrop(f),this.tree.onNodeDrop.emit({originalEvent:e,dragNode:n,dropNode:this.node,index:this.index}))}this.draghoverPrev=!1,this.draghoverNext=!1}processPointDrop(e){let i=e.dropNode.parent?e.dropNode.parent.children:this.tree.value;e.dragNodeSubNodes.splice(e.dragNodeIndex,1);let n=this.index;e.position<0?(n=e.dragNodeSubNodes===i?e.dragNodeIndex>e.index?e.index:e.index-1:e.index,i.splice(n,0,e.dragNode)):(n=i.length,i.push(e.dragNode)),this.tree.dragDropService.stopDrag({node:e.dragNode,subNodes:e.dropNode.parent?e.dropNode.parent.children:this.tree.value,index:e.dragNodeIndex})}createDropPointEventMetadata(e){return{dragNode:this.tree.dragNode,dragNodeIndex:this.tree.dragNodeIndex,dragNodeSubNodes:this.tree.dragNodeSubNodes,dropNode:this.node,index:this.index,position:e}}onDropPointDragOver(e){e.dataTransfer.dropEffect="move",e.preventDefault()}onDropPointDragEnter(e,i){this.tree.allowDrop(this.tree.dragNode,this.node,this.tree.dragNodeScope)&&(i<0?this.draghoverPrev=!0:this.draghoverNext=!0)}onDropPointDragLeave(e){this.draghoverPrev=!1,this.draghoverNext=!1}onDragStart(e){this.tree.draggableNodes&&this.node.draggable!==!1?(e.dataTransfer.setData("text","data"),this.tree.dragDropService.startDrag({tree:this,node:this.node,subNodes:this.node?.parent?this.node.parent.children:this.tree.value,index:this.index,scope:this.tree.draggableScope})):e.preventDefault()}onDragStop(e){this.tree.dragDropService.stopDrag({node:this.node,subNodes:this.node?.parent?this.node.parent.children:this.tree.value,index:this.index})}onDropNodeDragOver(e){e.dataTransfer.dropEffect="move",this.tree.droppableNodes&&(e.preventDefault(),e.stopPropagation())}onDropNode(e){if(this.tree.droppableNodes&&this.node?.droppable!==!1){let i=this.tree.dragNode;if(this.tree.allowDrop(i,this.node,this.tree.dragNodeScope)){let n=V({},this.createDropNodeEventMetadata());this.tree.validateDrop?this.tree.onNodeDrop.emit({originalEvent:e,dragNode:i,dropNode:this.node,index:this.index,accept:()=>{this.processNodeDrop(n)}}):(this.processNodeDrop(n),this.tree.onNodeDrop.emit({originalEvent:e,dragNode:i,dropNode:this.node,index:this.index}))}}e.preventDefault(),e.stopPropagation(),this.draghoverNode=!1}createDropNodeEventMetadata(){return{dragNode:this.tree.dragNode,dragNodeIndex:this.tree.dragNodeIndex,dragNodeSubNodes:this.tree.dragNodeSubNodes,dropNode:this.node}}processNodeDrop(e){let i=e.dragNodeIndex;e.dragNodeSubNodes.splice(i,1),e.dropNode.children?e.dropNode.children.push(e.dragNode):e.dropNode.children=[e.dragNode],this.tree.dragDropService.stopDrag({node:e.dragNode,subNodes:e.dropNode.parent?e.dropNode.parent.children:this.tree.value,index:i})}onDropNodeDragEnter(e){this.tree.droppableNodes&&this.node?.droppable!==!1&&this.tree.allowDrop(this.tree.dragNode,this.node,this.tree.dragNodeScope)&&(this.draghoverNode=!0)}onDropNodeDragLeave(e){if(this.tree.droppableNodes){let i=e.currentTarget.getBoundingClientRect();(e.x>i.left+i.width||e.x<i.left||e.y>=Math.floor(i.top+i.height)||e.y<i.top)&&(this.draghoverNode=!1)}}onKeyDown(e){if(!(!this.isSameNode(e)||this.tree.contextMenu&&this.tree.contextMenu.containerViewChild?.nativeElement.style.display==="block"))switch(e.code){case"ArrowDown":this.onArrowDown(e);break;case"ArrowUp":this.onArrowUp(e);break;case"ArrowRight":this.onArrowRight(e);break;case"ArrowLeft":this.onArrowLeft(e);break;case"Enter":case"Space":case"NumpadEnter":this.onEnter(e);break;case"Tab":this.setAllNodesTabIndexes();break;default:break}}onArrowUp(e){let i=e.target.getAttribute("data-pc-section")==="toggler"?e.target.closest('[role="treeitem"]'):e.target.parentElement;if(i.previousElementSibling)this.focusRowChange(i,i.previousElementSibling,this.findLastVisibleDescendant(i.previousElementSibling));else{let n=this.getParentNodeElement(i);n&&this.focusRowChange(i,n)}e.preventDefault()}onArrowDown(e){let i=e.target.getAttribute("data-pc-section")==="toggler"?e.target.closest('[role="treeitem"]'):e.target,n=i.children[1];if(n&&n.children.length>0)this.focusRowChange(i,n.children[0]);else if(i.parentElement.nextElementSibling)this.focusRowChange(i,i.parentElement.nextElementSibling);else{let o=this.findNextSiblingOfAncestor(i.parentElement);o&&this.focusRowChange(i,o)}e.preventDefault()}onArrowRight(e){!this.node?.expanded&&!this.tree.isNodeLeaf(this.node)&&(this.expand(e),e.currentTarget.tabIndex=-1,setTimeout(()=>{this.onArrowDown(e)},1)),e.preventDefault()}onArrowLeft(e){let i=e.target.getAttribute("data-pc-section")==="toggler"?e.target.closest('[role="treeitem"]'):e.target;if(this.level===0&&!this.node?.expanded)return!1;if(this.node?.expanded){this.collapse(e);return}let n=this.getParentNodeElement(i.parentElement);n&&this.focusRowChange(e.currentTarget,n),e.preventDefault()}onEnter(e){this.tree.onNodeClick(e,this.node),this.setTabIndexForSelectionMode(e,this.tree.nodeTouched),e.preventDefault()}setAllNodesTabIndexes(){let e=Z(this.tree.el.nativeElement,".p-tree-node"),i=[...e].some(n=>n.getAttribute("aria-selected")==="true"||n.getAttribute("aria-checked")==="true");if([...e].forEach(n=>{n.tabIndex=-1}),i){let n=[...e].filter(o=>o.getAttribute("aria-selected")==="true"||o.getAttribute("aria-checked")==="true");n[0].tabIndex=0;return}e.length&&([...e][0].tabIndex=0)}setTabIndexForSelectionMode(e,i){if(this.tree.selectionMode!==null){let n=[...Z(this.tree.el.nativeElement,'[role="treeitem"]')];e.currentTarget.tabIndex=i===!1?-1:0,n.every(o=>o.tabIndex===-1)&&(n[0].tabIndex=0)}}findNextSiblingOfAncestor(e){let i=this.getParentNodeElement(e);return i?i.nextElementSibling?i.nextElementSibling:this.findNextSiblingOfAncestor(i):null}findLastVisibleDescendant(e){let n=Array.from(e.children).find(o=>A(o,"p-tree-node"))?.children[1];if(n&&n.children.length>0){let o=n.children[n.children.length-1];return this.findLastVisibleDescendant(o)}else return e}getParentNodeElement(e){let i=e.parentElement?.parentElement?.parentElement;return i?.tagName==="P-TREENODE"?i:null}focusNode(e){this.tree.droppableNodes?e.children[1].focus():e.children[0].focus()}focusRowChange(e,i,n){e.tabIndex="-1",i.children[0].tabIndex="0",this.focusNode(n||i)}focusVirtualNode(){this.timeout=setTimeout(()=>{let e=he(document.body,`[data-id="${this.node?.key??this.node?.data}"]`);_e(e)},1)}static \u0275fac=(()=>{let e;return function(n){return(e||(e=P(t)))(n||t)}})();static \u0275cmp=Q({type:t,selectors:[["p-treeNode"]],inputs:{rowNode:"rowNode",node:"node",parentNode:"parentNode",root:[2,"root","root",m],index:[2,"index","index",E],firstChild:[2,"firstChild","firstChild",m],lastChild:[2,"lastChild","lastChild",m],level:[2,"level","level",E],indentation:[2,"indentation","indentation",E],itemSize:[2,"itemSize","itemSize",E],loadingMode:"loadingMode"},features:[B],decls:1,vars:1,consts:[["icon",""],["class","p-tree-node-droppoint",3,"ngClass","drop","dragover","dragenter","dragleave",4,"ngIf"],["role","treeitem",3,"keydown","ngClass","ngStyle"],[3,"click","contextmenu","dblclick","touchend","drop","dragover","dragenter","dragleave","dragstart","dragend","ngClass","draggable"],["type","button","pRipple","","tabindex","-1",1,"p-tree-node-toggle-button",3,"click"],[4,"ngIf"],["class","p-tree-node-toggle-icon",4,"ngIf"],["styleClass","p-tree-node-checkbox",3,"ngModel","binary","indeterminate","disabled","variant","tabindex","click",4,"ngIf"],[3,"class",4,"ngIf"],[1,"p-tree-node-label"],["class","p-tree-node-children","style","display: none;","role","group",3,"display",4,"ngIf"],[1,"p-tree-node-droppoint",3,"drop","dragover","dragenter","dragleave","ngClass"],[3,"styleClass",4,"ngIf"],[3,"styleClass"],[1,"p-tree-node-toggle-icon"],[4,"ngTemplateOutlet","ngTemplateOutletContext"],["styleClass","p-tree-node-checkbox",3,"click","ngModel","binary","indeterminate","disabled","variant","tabindex"],["role","group",1,"p-tree-node-children",2,"display","none"],[3,"node","parentNode","firstChild","lastChild","index","itemSize","level","loadingMode",4,"ngFor","ngForOf","ngForTrackBy"],[3,"node","parentNode","firstChild","lastChild","index","itemSize","level","loadingMode"]],template:function(i,n){i&1&&c(0,st,13,31),i&2&&H(n.node?0:-1)},dependencies:[t,X,j,W,G,J,Y,be,Ve,ne,ve,Se,ye,Te,ie,z],encapsulation:2})}return t})(),$e=(()=>{class t extends te{dragDropService;value;selectionMode;loadingMode="mask";selection;style;styleClass;contextMenu;draggableScope;droppableScope;draggableNodes;droppableNodes;metaKeySelection=!1;propagateSelectionUp=!0;propagateSelectionDown=!0;loading;loadingIcon;emptyMessage="";ariaLabel;togglerAriaLabel;ariaLabelledBy;validateDrop;filter;filterInputAutoFocus=!1;filterBy="label";filterMode="lenient";filterOptions;filterPlaceholder;filteredNodes;filterLocale;scrollHeight;lazy=!1;virtualScroll;virtualScrollItemSize;virtualScrollOptions;indentation=1.5;_templateMap;trackBy=(e,i)=>i;highlightOnSelect=!1;_virtualNodeHeight;get virtualNodeHeight(){return this._virtualNodeHeight}set virtualNodeHeight(e){this._virtualNodeHeight=e,console.log("The virtualNodeHeight property is deprecated, use virtualScrollItemSize property instead.")}selectionChange=new T;onNodeSelect=new T;onNodeUnselect=new T;onNodeExpand=new T;onNodeCollapse=new T;onNodeContextMenuSelect=new T;onNodeDoubleClick=new T;onNodeDrop=new T;onLazyLoad=new T;onScroll=new T;onScrollIndexChange=new T;onFilter=new T;filterTemplate;nodeTemplate;headerTemplate;footerTemplate;loaderTemplate;emptyMessageTemplate;togglerIconTemplate;checkboxIconTemplate;loadingIconTemplate;filterIconTemplate;filterViewChild;scroller;wrapperViewChild;templates;_headerTemplate;_emptyMessageTemplate;_footerTemplate;_loaderTemplate;_togglerIconTemplate;_checkboxIconTemplate;_loadingIconTemplate;_filterIconTemplate;_filterTemplate;ngAfterContentInit(){this.templates.length&&(this._templateMap={}),this.templates.forEach(e=>{switch(e.getType()){case"header":this._headerTemplate=e.template;break;case"empty":this._emptyMessageTemplate=e.template;break;case"footer":this._footerTemplate=e.template;break;case"loader":this._loaderTemplate=e.template;break;case"togglericon":this._togglerIconTemplate=e.template;break;case"checkboxicon":this._checkboxIconTemplate=e.template;break;case"loadingicon":this._loadingIconTemplate=e.template;break;case"filtericon":this._filterIconTemplate=e.template;break;case"filter":this._filterTemplate=e.template;break;default:this._templateMap[e.name]=e.template;break}})}serializedValue;nodeTouched;dragNodeTree;dragNode;dragNodeSubNodes;dragNodeIndex;dragNodeScope;dragHover;dragStartSubscription;dragStopSubscription;_componentStyle=R(Oe);constructor(e){super(),this.dragDropService=e}ngOnInit(){super.ngOnInit(),this.filterBy&&(this.filterOptions={filter:e=>this._filter(e),reset:()=>this.resetFilter()}),this.droppableNodes&&(this.dragStartSubscription=this.dragDropService.dragStart$.subscribe(e=>{this.dragNodeTree=e.tree,this.dragNode=e.node,this.dragNodeSubNodes=e.subNodes,this.dragNodeIndex=e.index,this.dragNodeScope=e.scope}),this.dragStopSubscription=this.dragDropService.dragStop$.subscribe(e=>{this.dragNodeTree=null,this.dragNode=null,this.dragNodeSubNodes=null,this.dragNodeIndex=null,this.dragNodeScope=null,this.dragHover=!1}))}ngOnChanges(e){super.ngOnChanges(e),e.value&&(this.updateSerializedValue(),this.hasFilterActive()&&this._filter(this.filterViewChild.nativeElement.value))}get containerClass(){return this._componentStyle.classes.root({instance:this})}get emptyMessageLabel(){return this.emptyMessage||this.config.getTranslation(me.EMPTY_MESSAGE)}updateSerializedValue(){this.serializedValue=[],this.serializeNodes(null,this.getRootNode(),0,!0)}serializeNodes(e,i,n,o){if(i&&i.length)for(let r of i){r.parent=e;let p={node:r,parent:e,level:n,visible:o&&(e?e.expanded:!0)};this.serializedValue.push(p),p.visible&&r.expanded&&this.serializeNodes(r,r.children,n+1,p.visible)}}onNodeClick(e,i){let n=e.target;if(!(A(n,"p-tree-toggler")||A(n,"p-tree-toggler-icon"))){if(this.selectionMode){if(i.selectable===!1){i.style="--p-focus-ring-color: none;";return}else i.style?.includes("--p-focus-ring-color")||(i.style=i.style?`${i.style}--p-focus-ring-color: var(--primary-color)`:"--p-focus-ring-color: var(--primary-color)");if(this.hasFilteredNodes()&&(i=this.getNodeWithKey(i.key,this.filteredNodes),!i))return;let o=this.findIndexInSelection(i),r=o>=0;if(this.isCheckboxSelectionMode())r?(this.propagateSelectionDown?this.propagateDown(i,!1):this.selection=this.selection.filter((p,f)=>f!=o),this.propagateSelectionUp&&i.parent&&this.propagateUp(i.parent,!1),this.selectionChange.emit(this.selection),this.onNodeUnselect.emit({originalEvent:e,node:i})):(this.propagateSelectionDown?this.propagateDown(i,!0):this.selection=[...this.selection||[],i],this.propagateSelectionUp&&i.parent&&this.propagateUp(i.parent,!0),this.selectionChange.emit(this.selection),this.onNodeSelect.emit({originalEvent:e,node:i}));else if(this.nodeTouched?!1:this.metaKeySelection){let f=e.metaKey||e.ctrlKey;r&&f?(this.isSingleSelectionMode()?this.selectionChange.emit(null):(this.selection=this.selection.filter((b,Ae)=>Ae!=o),this.selectionChange.emit(this.selection)),this.onNodeUnselect.emit({originalEvent:e,node:i})):(this.isSingleSelectionMode()?this.selectionChange.emit(i):this.isMultipleSelectionMode()&&(this.selection=f?this.selection||[]:[],this.selection=[...this.selection,i],this.selectionChange.emit(this.selection)),this.onNodeSelect.emit({originalEvent:e,node:i}))}else this.isSingleSelectionMode()?r?(this.selection=null,this.onNodeUnselect.emit({originalEvent:e,node:i})):(this.selection=i,setTimeout(()=>{this.onNodeSelect.emit({originalEvent:e,node:i})})):r?(this.selection=this.selection.filter((f,b)=>b!=o),this.onNodeUnselect.emit({originalEvent:e,node:i})):(this.selection=[...this.selection||[],i],setTimeout(()=>{this.onNodeSelect.emit({originalEvent:e,node:i})})),this.selectionChange.emit(this.selection)}this.nodeTouched=!1}}onNodeTouchEnd(){this.nodeTouched=!0}onNodeRightClick(e,i){if(this.contextMenu){let n=e.target;if(n.className&&n.className.indexOf("p-tree-toggler")===0)return;this.findIndexInSelection(i)>=0||(this.isSingleSelectionMode()?this.selectionChange.emit(i):this.selectionChange.emit([i])),this.contextMenu.show(e),this.onNodeContextMenuSelect.emit({originalEvent:e,node:i})}}onNodeDblClick(e,i){this.onNodeDoubleClick.emit({originalEvent:e,node:i})}findIndexInSelection(e){let i=-1;if(this.selectionMode&&this.selection)if(this.isSingleSelectionMode())i=this.selection.key&&this.selection.key===e.key||this.selection==e?0:-1;else for(let n=0;n<this.selection.length;n++){let o=this.selection[n];if(o.key&&o.key===e.key||o==e){i=n;break}}return i}syncNodeOption(e,i,n,o){let r=this.hasFilteredNodes()?this.getNodeWithKey(e.key,i):null;r&&(r[n]=o||e[n])}hasFilteredNodes(){return this.filter&&this.filteredNodes&&this.filteredNodes.length}hasFilterActive(){return this.filter&&this.filterViewChild?.nativeElement?.value.length>0}getNodeWithKey(e,i){for(let n of i){if(n.key===e)return n;if(n.children){let o=this.getNodeWithKey(e,n.children);if(o)return o}}}propagateUp(e,i){if(e.children&&e.children.length){let o=0,r=!1;for(let p of e.children)this.isSelected(p)?o++:p.partialSelected&&(r=!0);if(i&&o==e.children.length)this.selection=[...this.selection||[],e],e.partialSelected=!1;else{if(!i){let p=this.findIndexInSelection(e);p>=0&&(this.selection=this.selection.filter((f,b)=>b!=p))}r||o>0&&o!=e.children.length?e.partialSelected=!0:e.partialSelected=!1}this.syncNodeOption(e,this.filteredNodes,"partialSelected")}let n=e.parent;n&&this.propagateUp(n,i)}propagateDown(e,i){let n=this.findIndexInSelection(e);if(i&&n==-1?this.selection=[...this.selection||[],e]:!i&&n>-1&&(this.selection=this.selection.filter((o,r)=>r!=n)),e.partialSelected=!1,this.syncNodeOption(e,this.filteredNodes,"partialSelected"),e.children&&e.children.length)for(let o of e.children)this.propagateDown(o,i)}isSelected(e){return this.findIndexInSelection(e)!=-1}isSingleSelectionMode(){return this.selectionMode&&this.selectionMode=="single"}isMultipleSelectionMode(){return this.selectionMode&&this.selectionMode=="multiple"}isCheckboxSelectionMode(){return this.selectionMode&&this.selectionMode=="checkbox"}isNodeLeaf(e){return e.leaf==!1?!1:!(e.children&&e.children.length)}getRootNode(){return this.filteredNodes?this.filteredNodes:this.value}getTemplateForNode(e){return this._templateMap?e.type?this._templateMap[e.type]:this._templateMap.default:null}onDragOver(e){this.droppableNodes&&(!this.value||this.value.length===0)&&(e.dataTransfer.dropEffect="move",e.preventDefault())}onDrop(e){if(this.droppableNodes&&(!this.value||this.value.length===0)){e.preventDefault();let i=this.dragNode;if(this.allowDrop(i,null,this.dragNodeScope)){let n=this.dragNodeIndex;this.value=this.value||[],this.validateDrop?this.onNodeDrop.emit({originalEvent:e,dragNode:i,dropNode:null,index:n,accept:()=>{this.processTreeDrop(i,n)}}):(this.onNodeDrop.emit({originalEvent:e,dragNode:i,dropNode:null,index:n}),this.processTreeDrop(i,n))}}}processTreeDrop(e,i){this.dragNodeSubNodes.splice(i,1),this.value.push(e),this.dragDropService.stopDrag({node:e})}onDragEnter(){this.droppableNodes&&this.allowDrop(this.dragNode,null,this.dragNodeScope)&&(this.dragHover=!0)}onDragLeave(e){if(this.droppableNodes){let i=e.currentTarget.getBoundingClientRect();(e.x>i.left+i.width||e.x<i.left||e.y>i.top+i.height||e.y<i.top)&&(this.dragHover=!1)}}allowDrop(e,i,n){if(e)if(this.isValidDragScope(n)){let o=!0;if(i)if(e===i)o=!1;else{let r=i.parent;for(;r!=null;){if(r===e){o=!1;break}r=r.parent}}return o}else return!1;else return!1}isValidDragScope(e){let i=this.droppableScope;if(i){if(typeof i=="string"){if(typeof e=="string")return i===e;if(Array.isArray(e))return e.indexOf(i)!=-1}else if(Array.isArray(i)){if(typeof e=="string")return i.indexOf(e)!=-1;if(Array.isArray(e)){for(let n of i)for(let o of e)if(n===o)return!0}}return!1}else return!0}_filter(e){let i=e;if(i==="")this.filteredNodes=null;else{this.filteredNodes=[];let n=this.filterBy.split(","),o=ee(i).toLocaleLowerCase(this.filterLocale),r=this.filterMode==="strict";for(let p of this.value){let f=V({},p),b={searchFields:n,filterText:o,isStrictMode:r};(r&&(this.findFilteredNodes(f,b)||this.isFilterMatched(f,b))||!r&&(this.isFilterMatched(f,b)||this.findFilteredNodes(f,b)))&&this.filteredNodes.push(f)}}this.updateSerializedValue(),this.onFilter.emit({filter:i,filteredValue:this.filteredNodes})}resetFilter(){this.filteredNodes=null,this.filterViewChild&&this.filterViewChild.nativeElement&&(this.filterViewChild.nativeElement.value="")}scrollToVirtualIndex(e){this.virtualScroll&&this.scroller?.scrollToIndex(e)}scrollTo(e){this.virtualScroll?this.scroller?.scrollTo(e):this.wrapperViewChild&&this.wrapperViewChild.nativeElement&&(this.wrapperViewChild.nativeElement.scrollTo?this.wrapperViewChild.nativeElement.scrollTo(e):(this.wrapperViewChild.nativeElement.scrollLeft=e.left,this.wrapperViewChild.nativeElement.scrollTop=e.top))}findFilteredNodes(e,i){if(e){let n=!1;if(e.children){let o=[...e.children];e.children=[];for(let r of o){let p=V({},r);this.isFilterMatched(p,i)&&(n=!0,e.children.push(p))}}if(n)return e.expanded=!0,!0}}isFilterMatched(e,i){let{searchFields:n,filterText:o,isStrictMode:r}=i,p=!1;for(let f of n)ee(String(ue(e,f))).toLocaleLowerCase(this.filterLocale).indexOf(o)>-1&&(p=!0);return(!p||r&&!this.isNodeLeaf(e))&&(p=this.findFilteredNodes(e,{searchFields:n,filterText:o,isStrictMode:r})||p),p}getIndex(e,i){let n=e.getItemOptions;return n?n(i).index:i}getBlockableElement(){return this.el.nativeElement.children[0]}ngOnDestroy(){this.dragStartSubscription&&this.dragStartSubscription.unsubscribe(),this.dragStopSubscription&&this.dragStopSubscription.unsubscribe(),super.ngOnDestroy()}static \u0275fac=function(i){return new(i||t)(de(xe,8))};static \u0275cmp=Q({type:t,selectors:[["p-tree"]],contentQueries:function(i,n,o){if(i&1&&(y(o,Fe,4),y(o,ct,4),y(o,pt,4),y(o,gt,4),y(o,ht,4),y(o,_t,4),y(o,ut,4),y(o,ft,4),y(o,mt,4),y(o,xt,4),y(o,fe,4)),i&2){let r;x(r=N())&&(n.filterTemplate=r.first),x(r=N())&&(n.nodeTemplate=r.first),x(r=N())&&(n.headerTemplate=r.first),x(r=N())&&(n.footerTemplate=r.first),x(r=N())&&(n.loaderTemplate=r.first),x(r=N())&&(n.emptyMessageTemplate=r.first),x(r=N())&&(n.togglerIconTemplate=r.first),x(r=N())&&(n.checkboxIconTemplate=r.first),x(r=N())&&(n.loadingIconTemplate=r.first),x(r=N())&&(n.filterIconTemplate=r.first),x(r=N())&&(n.templates=r)}},viewQuery:function(i,n){if(i&1&&($(Fe,5),$(Nt,5),$(Tt,5)),i&2){let o;x(o=N())&&(n.filterViewChild=o.first),x(o=N())&&(n.scroller=o.first),x(o=N())&&(n.wrapperViewChild=o.first)}},inputs:{value:"value",selectionMode:"selectionMode",loadingMode:"loadingMode",selection:"selection",style:"style",styleClass:"styleClass",contextMenu:"contextMenu",draggableScope:"draggableScope",droppableScope:"droppableScope",draggableNodes:[2,"draggableNodes","draggableNodes",m],droppableNodes:[2,"droppableNodes","droppableNodes",m],metaKeySelection:[2,"metaKeySelection","metaKeySelection",m],propagateSelectionUp:[2,"propagateSelectionUp","propagateSelectionUp",m],propagateSelectionDown:[2,"propagateSelectionDown","propagateSelectionDown",m],loading:[2,"loading","loading",m],loadingIcon:"loadingIcon",emptyMessage:"emptyMessage",ariaLabel:"ariaLabel",togglerAriaLabel:"togglerAriaLabel",ariaLabelledBy:"ariaLabelledBy",validateDrop:[2,"validateDrop","validateDrop",m],filter:[2,"filter","filter",m],filterInputAutoFocus:[2,"filterInputAutoFocus","filterInputAutoFocus",m],filterBy:"filterBy",filterMode:"filterMode",filterOptions:"filterOptions",filterPlaceholder:"filterPlaceholder",filteredNodes:"filteredNodes",filterLocale:"filterLocale",scrollHeight:"scrollHeight",lazy:[2,"lazy","lazy",m],virtualScroll:[2,"virtualScroll","virtualScroll",m],virtualScrollItemSize:[2,"virtualScrollItemSize","virtualScrollItemSize",E],virtualScrollOptions:"virtualScrollOptions",indentation:[2,"indentation","indentation",E],_templateMap:"_templateMap",trackBy:"trackBy",highlightOnSelect:[2,"highlightOnSelect","highlightOnSelect",m],virtualNodeHeight:"virtualNodeHeight"},outputs:{selectionChange:"selectionChange",onNodeSelect:"onNodeSelect",onNodeUnselect:"onNodeUnselect",onNodeExpand:"onNodeExpand",onNodeCollapse:"onNodeCollapse",onNodeContextMenuSelect:"onNodeContextMenuSelect",onNodeDoubleClick:"onNodeDoubleClick",onNodeDrop:"onNodeDrop",onLazyLoad:"onLazyLoad",onScroll:"onScroll",onScrollIndexChange:"onScrollIndexChange",onFilter:"onFilter"},features:[ge([Oe]),B,ae],decls:8,vars:10,consts:[["filter",""],["scroller",""],["content",""],["treeNode",""],["loader",""],["wrapper",""],["emptyFilter",""],[3,"drop","dragover","dragenter","dragleave","ngClass","ngStyle"],["class","p-tree-mask p-overlay-mask",4,"ngIf"],[4,"ngTemplateOutlet"],[4,"ngIf"],["class","p-tree-empty-message",4,"ngIf"],[1,"p-tree-mask","p-overlay-mask"],[3,"class",4,"ngIf"],[3,"spin","styleClass",4,"ngIf"],["class","p-tree-loading-icon",4,"ngIf"],[3,"spin","styleClass"],[1,"p-tree-loading-icon"],[4,"ngTemplateOutlet","ngTemplateOutletContext"],["pInputText","","type","search","autocomplete","off",1,"p-tree-filter-input",3,"keydown.enter","input","pAutoFocus"],["class","p-tree-filter-icon",4,"ngIf"],[1,"p-tree-filter-icon"],["styleClass","p-tree-root",3,"items","tabindex","style","scrollHeight","itemSize","lazy","options","onScroll","onScrollIndexChange","onLazyLoad",4,"ngIf"],["styleClass","p-tree-root",3,"onScroll","onScrollIndexChange","onLazyLoad","items","tabindex","scrollHeight","itemSize","lazy","options"],["class","p-tree-root-children","role","tree",3,"ngClass","style",4,"ngIf"],["role","tree",1,"p-tree-root-children",3,"ngClass"],[3,"level","rowNode","node","parentNode","firstChild","lastChild","index","itemSize","indentation","loadingMode",4,"ngFor","ngForOf","ngForTrackBy"],[3,"level","rowNode","node","parentNode","firstChild","lastChild","index","itemSize","indentation","loadingMode"],[1,"p-tree-root"],["class","p-tree-root-children","role","tree",4,"ngIf"],["role","tree",1,"p-tree-root-children"],[3,"node","firstChild","lastChild","index","level","loadingMode",4,"ngFor","ngForOf","ngForTrackBy"],[3,"node","firstChild","lastChild","index","level","loadingMode"],[1,"p-tree-empty-message"],[4,"ngIf","ngIfElse"]],template:function(i,n){if(i&1&&(_(0,"div",7),I("drop",function(r){return n.onDrop(r)})("dragover",function(r){return n.onDragOver(r)})("dragenter",function(){return n.onDragEnter()})("dragleave",function(r){return n.onDragLeave(r)}),c(1,Dt,3,2,"div",8)(2,kt,1,0,"ng-container",9)(3,Mt,1,4,"ng-container")(4,Ut,1,1,"p-iconField")(5,Wt,3,2,"ng-container",10)(6,Xt,3,3,"div",11)(7,Zt,1,0,"ng-container",9),u()),i&2){let o;F(n.styleClass),a("ngClass",n.containerClass)("ngStyle",n.style),s(),a("ngIf",n.loading&&n.loadingMode==="mask"),s(),a("ngTemplateOutlet",n.headerTemplate||n._headerTemplate),s(),H(n.filterTemplate||n._filterTemplate?3:4),s(2),a("ngIf",(o=n.getRootNode())==null?null:o.length),s(),a("ngIf",!n.loading&&(n.getRootNode()==null||n.getRootNode().length===0)),s(),a("ngTemplateOutlet",n.footerTemplate||n._footerTemplate)}},dependencies:[X,j,W,G,J,Y,Me,z,Ce,ie,De,ne,ke,Ee,ii,we,Ie],encapsulation:2})}return t})(),Oi=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275mod=se({type:t});static \u0275inj=le({imports:[$e,z,z]})}return t})();export{$e as a,Oi as b};
