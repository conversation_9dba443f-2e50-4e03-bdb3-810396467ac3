import{c as p}from"./chunk-MMC3E6BY.js";import{T as a,Y as i}from"./chunk-YUW2MUHJ.js";import{a as o}from"./chunk-EQDQRRRY.js";var s=class r{constructor(e){this.restService=e;this.apiName="Default";this.getChapterTreeByEBookId=(e,t)=>this.restService.request({method:"GET",url:`/api/app/read-only-eBook/chapter-tree-by-eBook-id/${e}`},o({apiName:this.apiName},t));this.getEBooksByChannelId=(e,t)=>this.restService.request({method:"GET",url:`/api/app/read-only-eBook/e-books-by-channel-id/${e}`},o({apiName:this.apiName},t));this.getMatchedEBook=(e,t)=>this.restService.request({method:"GET",url:`/api/app/read-only-eBook/matched-eBook/${e}`},o({apiName:this.apiName},t));this.getMatchedEBookByDeliveryDate=(e,t)=>this.restService.request({method:"GET",url:"/api/app/read-only-eBook/matched-eBook-by-delivery-date",params:{deliveryDate:e}},o({apiName:this.apiName},t))}static{this.\u0275fac=function(t){return new(t||r)(i(p))}}static{this.\u0275prov=a({token:r,factory:r.\u0275fac,providedIn:"root"})}};export{s as a};
