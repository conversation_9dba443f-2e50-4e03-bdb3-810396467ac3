import{a as U}from"./chunk-MMC3E6BY.js";import{a as f}from"./chunk-GDGXRFMB.js";import{Ac as u,T as m,Z as h,ta as i}from"./chunk-YUW2MUHJ.js";import{f as s}from"./chunk-EQDQRRRY.js";var r=[];for(let t=0;t<256;++t)r.push((t+256).toString(16).slice(1));function y(t,e=0){return(r[t[e+0]]+r[t[e+1]]+r[t[e+2]]+r[t[e+3]]+"-"+r[t[e+4]]+r[t[e+5]]+"-"+r[t[e+6]]+r[t[e+7]]+"-"+r[t[e+8]]+r[t[e+9]]+"-"+r[t[e+10]]+r[t[e+11]]+r[t[e+12]]+r[t[e+13]]+r[t[e+14]]+r[t[e+15]]).toLowerCase()}var d,x=new Uint8Array(16);function l(){if(!d){if(typeof crypto>"u"||!crypto.getRandomValues)throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");d=crypto.getRandomValues.bind(crypto)}return d(x)}var D=typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto),c={randomUUID:D};function b(t,e,n){if(c.randomUUID&&!e&&!t)return c.randomUUID();t=t||{};let o=t.random??t.rng?.()??l();if(o.length<16)throw new Error("Random bytes length must be >= 16");if(o[6]=o[6]&15|64,o[8]=o[8]&63|128,e){if(n=n||0,n<0||n+16>e.length)throw new RangeError(`UUID byte range ${n}:${n+15} is out of buffer bounds`);for(let a=0;a<16;++a)e[n+a]=o[a];return e}return y(o)}var g=b;var I=class t{constructor(){this.document=h(f);this.supportedLanguages=[{code:"zh-Hans",label:"\u7B80\u4F53\u4E2D\u6587"},{code:"zh-Hant",label:"\u7E41\u9AD4\u4E2D\u6587"},{code:"en",label:"English"}];this.supportedAudioDevices=[{code:"cmn",label:"\u666E\u901A\u8BDD"},{code:"yue",label:"\u7CA4\u8BED"},{code:"eng",label:"English"}];this.language=i(this.getStoredLanguage()||"zh-Hans");this.audioDevice=i(this.getStoredAudioDevice()||"cmn");this.language$=U(this.language);this.translations=i({});this.currentLanguageInfo=u(()=>this.supportedLanguages.find(e=>e.code===this.language())||this.supportedLanguages[0]);this.currentAudioDeviceInfo=u(()=>this.supportedAudioDevices.find(e=>e.code===this.audioDevice())||this.supportedAudioDevices[0]);this.ip=i("");this.userUUID=i("");this.loadTranslations(),this.loadIPAndCountry(),this.loadUserUUID()}setLanguage(e){this.language.set(e),localStorage.setItem("lang",e),this.updateDocumentLanguage()}setAudioDevice(e){this.audioDevice.set(e),localStorage.setItem("audio",e)}getStoredLanguage(){return localStorage.getItem("lang")||"zh-Hans"}getStoredAudioDevice(){return localStorage.getItem("audio")||"cmn"}loadTranslations(){return s(this,null,function*(){try{let[e,n,o]=yield Promise.all([import("./chunk-RJ2IVYER.js"),import("./chunk-MALJPLTI.js"),import("./chunk-3NTXVGEG.js")]);this.translations.set({"zh-Hans":e.default,"zh-Hant":n.default,en:o.default})}catch(e){console.error("Failed to load translations:",e)}})}loadIPAndCountry(){return s(this,null,function*(){try{let n=yield(yield fetch("https://ipapi.co/json/")).json();n&&n.ip?this.ip.set(n.ip):(console.warn("IP data is missing:",n),this.ip.set(""))}catch(e){console.error("Failed to get IP data:",e)}})}loadUserUUID(){let e=localStorage.getItem("userUUID");if(e)this.userUUID.set(e);else{let n=this.generateUUID();this.userUUID.set(n),localStorage.setItem("userUUID",n)}}updateDocumentLanguage(){this.document.documentElement.lang=this.language()}translate(e,n){let o=this.translations()[this.language()];if(!o)return e;let a=this.getNestedValue(o,e);return typeof a!="string"?e:n?Object.keys(n).reduce((v,p)=>v.replace(new RegExp(`{{${p}}}`,"g"),n[p]),a):a}getNestedValue(e,n){return n.split(".").reduce((o,a)=>o&&typeof o=="object"?o[a]:void 0,e)||n}generateUUID(){return g()}static{this.\u0275fac=function(n){return new(n||t)}}static{this.\u0275prov=m({token:t,factory:t.\u0275fac,providedIn:"root"})}};export{I as a};
