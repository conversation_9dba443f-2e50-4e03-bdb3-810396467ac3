import { Component, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { ReadOnlyAlbumService } from '@/proxy/holy-bless/albums';
import { AlbumFileDto, AlbumAggregateDto } from '@/proxy/holy-bless/albums/dtos';
import { skip, Subscription } from 'rxjs';
import { I18nService } from '@/services/i18n.service';
import { ReadOnlyChannelService } from '@/proxy/holy-bless/channels';
import { RemoveExtensionPipe } from '@/pipes/remove-extension.pipe';
import { PlayerService } from '@/services/player.service';
import { LoadingService } from '@/services/loading.service';

@Component({
  selector: 'app-album-detail',
  standalone: true,
  imports: [CommonModule, TableModule, ButtonModule, RemoveExtensionPipe],
  templateUrl: './album-detail.html',
  styleUrls: ['./album-detail.scss'],
})
export class AlbumDetailComponent {
  route = inject(ActivatedRoute);
  #ReadOnlyAlbumService = inject(ReadOnlyAlbumService);
  products = signal<AlbumFileDto[]>([]);
  albumDetail = signal<AlbumAggregateDto | null>(null);
  subs = new Subscription();
  i18Service = inject(I18nService);
  #ReadOnlyChannelService = inject(ReadOnlyChannelService);
  router = inject(Router);
  selectedNode = signal<AlbumFileDto | null>(null);
  playerService = inject(PlayerService);
  loadingService = inject(LoadingService);

  ngOnInit() {
    this.route.params.subscribe({
      next: (param) => {
        const albumId = +param['albumId'];
        if (albumId) {
          this.loadAlbumDetails(albumId);
          this.changeLanguage(albumId);
        }
      },
    });
  }

  changeLanguage(albumId: number) {
    this.subs.unsubscribe();
    this.subs = new Subscription();
    const sub = this.i18Service.language$.pipe(skip(1)).subscribe((lang) => {
      this.#ReadOnlyChannelService
        .getMatchedChannelByVirtualFolderId(albumId)
        .subscribe({
          next: (channel) => {
            if (!channel) {
              this.router.navigateByUrl('/landing');
              return;
            }
            this.router.navigateByUrl(`/podcast/album-card/${channel.id}`);
          },
        });
    });
    this.subs.add(sub);
  }

  loadAlbumDetails(albumId: number) {
    if (!albumId) return;
    this.loadingService.show();
    this.#ReadOnlyAlbumService.getAlbumFiles(albumId).subscribe({
      next: (album) => {
        this.albumDetail.set(album);
        this.products.set(album.albumFiles || []);
        this.selectedNode.set(album.albumFiles[0]);
        this.loadingService.hide();
      },
      error: (error) => {
        console.error('Error loading album details:', error);
        this.loadingService.hide();
      },
    });
  }

  videoPlay(product: AlbumFileDto) {
    this.playerService.playVideo(product);
  }
}
