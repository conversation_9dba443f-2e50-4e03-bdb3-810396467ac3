import{a as te,b as ie}from"./chunk-JULIEJUT.js";import{a as G,b as X,c as Z}from"./chunk-YN5RRTSU.js";import"./chunk-2QGORWDL.js";import{a as K}from"./chunk-QJAYFQIR.js";import{a as ee}from"./chunk-MJMCW5UB.js";import{a as Q}from"./chunk-Y2PVWY7X.js";import"./chunk-MMC3E6BY.js";import"./chunk-KB23BJ64.js";import"./chunk-QAUHHFOT.js";import"./chunk-VBHJ4LBO.js";import{a as j,b as W}from"./chunk-QPTTFFWZ.js";import{c as U,d as V}from"./chunk-4JDMXTQG.js";import"./chunk-XXVSVAEK.js";import"./chunk-P4BOZY2U.js";import{c as q,d as Y}from"./chunk-FUCZYBK4.js";import"./chunk-OTT6DUE3.js";import"./chunk-22JWGO27.js";import{ba as A,ra as $,sa as J,ua as N}from"./chunk-SXMRENJM.js";import"./chunk-BMA7WWEI.js";import{D as L,G as z,i as g,k as H}from"./chunk-GDGXRFMB.js";import{$b as x,Ab as s,Bb as p,Fb as k,Kb as d,Lb as u,Oa as M,Ob as D,Pa as S,Ra as I,Ta as a,Vb as h,Xb as _,Z as c,Zb as R,_b as B,a as C,ac as F,cb as w,fa as b,ga as y,ib as f,kc as O,mc as E,qb as l,ta as v,xb as P,yb as T,zb as o}from"./chunk-YUW2MUHJ.js";import"./chunk-EQDQRRRY.js";var re=(n,e)=>e.id;function oe(n,e){if(n&1&&p(0,"img",16),n&2){let i=u().$implicit;l("src",i.thumbnailUrl,S)("alt",i.title+" \u5C01\u9762\u56FE\u7247")}}function ae(n,e){if(n&1&&(o(0,"div",17),p(1,"i",18),h(2),O(3,"date"),s()),n&2){let i=u().$implicit;a(2),_(" ",E(3,1,i.creationTime,"yyyy-MM-dd HH:mm:ss")," ")}}function se(n,e){if(n&1){let i=k();o(0,"p-card",9),f(1,oe,1,2,"ng-template",12),o(2,"p",13),d("click",function(){let m=b(i).$implicit,r=u();return y(r.navigateToArticle(m))}),s(),o(3,"p",14),h(4),s(),f(5,ae,4,4,"ng-template",15),s()}if(n&2){let i=e.$implicit;l("id","card-"+i.id),a(2),l("innerHTML",i.title,M),a(2),_(" ",i.description," ")}}var ne=class n{constructor(){this.home={icon:"pi pi-home",routerLink:"/"};this.breadcrumbItems=v([]);this.totalRecords=0;this.rows=10;this.first=0;this.isMobile=!1;this.selectedDate=null;this.contentCode=null;this.#e=c(K);this.i18nService=c(Q);this.#t=c(L);this.router=c(z);this.subs=new C;this.loadingService=c(ee);this.cardItems=[];this.name="";this.checkMobile()}#e;#t;ngOnInit(){this.#t.queryParams.subscribe(e=>{if(e.contentCode){this.contentCode=e.contentCode,this.subs.unsubscribe(),this.subs=new C;let i=this.i18nService.language$.subscribe(()=>{this.loadCollectionSummary()});this.subs.add(i)}})}initBreadcrumb(){let e=[{label:this.name}];this.breadcrumbItems.set(e)}loadCollectionSummary(){this.contentCode&&(this.loadingService.show(),this.#e.getCollectionSummaryByContentCode(this.contentCode,{skip:this.first,maxResultCount:this.rows,year:this.selectedDate?.getFullYear(),month:(this.selectedDate?.getMonth()||0)+1}).subscribe({next:e=>{this.name=e.name||"",this.cardItems=e.articles,this.totalRecords=e.totalRecords,this.initBreadcrumb(),this.loadingService.hide()},error:e=>{console.error("\u83B7\u53D6\u6458\u8981\u6570\u636E\u5931\u8D25:",e),this.loadingService.hide()}}))}navigateToArticle(e){e.id&&this.router.navigateByUrl(`/home/<USER>/${e.id}`)}onPageChange(e){this.first=e.first,this.rows=e.rows,this.loadCollectionSummary()}onDateChange(e){this.selectedDate=e,this.loadCollectionSummary()}get rowsPerPageOptions(){return this.isMobile?void 0:[10]}onResize(e){this.checkMobile()}checkMobile(){this.isMobile=window.innerWidth<=768}playCurrentPage(){}ngOnDestroy(){this.subs.unsubscribe()}static{this.\u0275fac=function(i){return new(i||n)}}static{this.\u0275cmp=w({type:n,selectors:[["app-image-cards"]],hostBindings:function(i,t){i&1&&d("resize",function(r){return t.onResize(r)},!1,I)},features:[F([g])],decls:13,vars:13,consts:[[1,"image-cards-container","p-6"],[1,"filters-toolbar","flex","justify-between","items-center","mb-6","rounded-lg"],[3,"model","home"],[1,"flex","items-center","gap-4"],[1,"date-picker-container"],[1,"p-float-label"],["showClear","","view","month","dateFormat","yy-mm",3,"onSelect","onClear","ngModelChange","ngModel","readonlyInput","placeholder"],["icon","pi pi-play-circle","severity","primary",3,"onClick","label","outlined"],[1,"grid","grid-cols-1","md:grid-cols-2","lg:grid-cols-5","gap-6","mb-8"],["styleClass","card-item",3,"id"],[1,"pagination-container"],["styleClass","custom-paginator",3,"onPageChange","first","rows","totalRecords","showPageLinks","showJumpToPageInput","showJumpToPageDropdown"],["pTemplate","header"],[1,"p-card-title","cursor-pointer",3,"click","innerHTML"],[1,"mt-2","text-gray-500","text-sm"],["pTemplate","footer"],[1,"card-image","rounded-t-xl",3,"src","alt"],[1,"text-gray-500","text-sm","flex","items-center"],[1,"pi","pi-clock","mr-2"]],template:function(i,t){i&1&&(o(0,"div",0)(1,"div",1),p(2,"p-breadcrumb",2),o(3,"div",3)(4,"div",4)(5,"span",5)(6,"p-datepicker",6),d("onSelect",function(r){return t.onDateChange(r)})("onClear",function(){return t.onDateChange(null)}),x("ngModelChange",function(r){return B(t.selectedDate,r)||(t.selectedDate=r),r}),s()()(),o(7,"p-button",7),d("onClick",function(){return t.playCurrentPage()}),s()()(),o(8,"div",8),P(9,se,6,3,"p-card",9,re),s(),o(11,"div",10)(12,"p-paginator",11),d("onPageChange",function(r){return t.onPageChange(r)}),s()()()),i&2&&(a(2),l("model",t.breadcrumbItems())("home",t.home),a(4),D("placeholder",t.i18nService.translate("selectDate")),R("ngModel",t.selectedDate),l("readonlyInput",!0),a(),l("label",t.i18nService.translate("play"))("outlined",!0),a(2),T(t.cardItems),a(3),l("first",t.first)("rows",t.rows)("totalRecords",t.totalRecords)("showPageLinks",!t.isMobile)("showJumpToPageInput",!t.isMobile)("showJumpToPageDropdown",t.isMobile))},dependencies:[H,g,W,j,A,V,U,G,Y,q,N,$,J,Z,X,ie,te],styles:["[_nghost-%COMP%]{flex:1}.card-image[_ngcontent-%COMP%]{width:100%;object-fit:cover;transition:transform .3s ease}.pagination-container[_ngcontent-%COMP%]{display:flex;justify-content:center;margin-top:2rem}.pagination-container[_ngcontent-%COMP%]   .custom-paginator[_ngcontent-%COMP%]{border:none;background:transparent}"]})}};export{ne as ImageCardsComponent};
