import{c as s}from"./chunk-MMC3E6BY.js";import{T as i,Y as l}from"./chunk-YUW2MUHJ.js";import{a as r}from"./chunk-EQDQRRRY.js";var o=class a{constructor(e){this.restService=e;this.apiName="Default";this.getArticleAggregate=(e,t)=>this.restService.request({method:"GET",url:`/api/app/read-only-article/article-aggregate/${e}`},r({apiName:this.apiName},t));this.getArticleAggregateByDeliveryDate=(e,t)=>this.restService.request({method:"GET",url:"/api/app/read-only-article/article-aggregate-by-delivery-date",params:{deliveryDate:e}},r({apiName:this.apiName},t));this.getArticleAggregatesByChapterId=(e,t,c=20,g)=>this.restService.request({method:"GET",url:`/api/app/read-only-article/article-aggregates-by-chapter-id/${e}`,params:{skipCount:t,maxResultCount:c}},r({apiName:this.apiName},g));this.getArticleAggregatesByCollectionContentCode=(e,t)=>this.restService.request({method:"GET",url:"/api/app/read-only-article/article-aggregates-by-collection-content-code",params:{collectionContentCode:e}},r({apiName:this.apiName},t));this.getArticleAggregatesByCollectionId=(e,t)=>this.restService.request({method:"GET",url:`/api/app/read-only-article/article-aggregates-by-collection-id/${e}`},r({apiName:this.apiName},t));this.getMatchedIdByArticleId=(e,t)=>this.restService.request({method:"GET",url:`/api/app/read-only-article/matched-id/${e}`},r({apiName:this.apiName},t));this.getRelatedArticlesByFileIdByFileId=(e,t)=>this.restService.request({method:"GET",url:`/api/app/read-only-article/related-articles-by-file-id/${e}`},r({apiName:this.apiName},t));this.search=(e,t)=>this.restService.request({method:"POST",url:"/api/app/read-only-article/search",body:e},r({apiName:this.apiName},t))}static{this.\u0275fac=function(t){return new(t||a)(l(s))}}static{this.\u0275prov=i({token:a,factory:a.\u0275fac,providedIn:"root"})}};export{o as a};
