import{a as Y,b as Z}from"./chunk-4UYMFPPF.js";import"./chunk-OLNEY35K.js";import{a as U}from"./chunk-JWVI4R37.js";import{a as X}from"./chunk-43MY25HH.js";import"./chunk-7SYDZWIG.js";import{a as J}from"./chunk-QJAYFQIR.js";import{a as Q}from"./chunk-MJMCW5UB.js";import{a as K}from"./chunk-Y2PVWY7X.js";import"./chunk-MMC3E6BY.js";import"./chunk-VBHJ4LBO.js";import"./chunk-22JWGO27.js";import"./chunk-SXMRENJM.js";import{C as W,D as G,d as z,e as V,i as $,k as j}from"./chunk-GDGXRFMB.js";import{$b as L,Ab as r,Ac as A,Bb as C,Fb as H,Kb as _,Lb as T,Oa as D,Ta as o,Vb as p,Wb as S,Xb as b,Z as d,Zb as R,_b as O,a as y,cb as k,fa as E,ga as F,ib as v,kc as I,lc as P,mc as q,qb as f,sa as w,ta as g,vb as x,wb as N,xb as M,yb as B,zb as c}from"./chunk-YUW2MUHJ.js";import"./chunk-EQDQRRRY.js";function ne(n,e){if(n&1&&(C(0,"i",12),p(1),I(2,"date")),n&2){let t,i=T();o(),b(" ",q(2,1,(t=i.articleDetail())==null?null:t.deliveryDate,"yy-MM-dd HH:mm:ss")," ")}}function re(n,e){n&1&&(c(0,"div",13),p(1," \u6682\u65E0\u76EE\u5F55 "),r())}function oe(n,e){if(n&1){let t=H();c(0,"a",14),_("click",function(){let a=E(t).$implicit,l=T();return F(l.onTocItemClick(a))}),p(1),r()}if(n&2){let t=e.$implicit;o(),b(" ",t.text," ")}}function le(n,e){if(n&1&&(c(0,"p",15)(1,"span"),p(2),I(3,"removeExtension"),r(),C(4,"i",16),r()),n&2){let t=e.$implicit;o(2),S(P(3,1,t.fileName))}}function ce(n,e){if(n&1&&(c(0,"h3"),p(1,"\u9644\u4EF6\u4E0B\u8F7D"),r(),M(2,le,5,3,"p",15,N)),n&2){let t=T();o(2),B(t.notImageArticleFiles())}}var ee=class n{constructor(){this.#e=d(J);this.#t=d(U);this.#i=d(G);this.sanitizer=d(W);this.elementRef=d(w);this.i18nService=d(K);this.subs=new y;this.loadingService=d(Q);this.contentCode=null;this.files=[];this.articleDetail=g(null);this.articleFiles=A(()=>this.articleDetail()?.articleFiles||[]);this.notImageArticleFiles=A(()=>this.articleDetail()?.articleFiles.filter(e=>e.mediaType!==0)||[]);this.tocItems=g([]);this.articleContent=g("")}#e;#t;#i;ngOnInit(){this.#i.queryParams.subscribe(e=>{if(!e.contentCode)return;this.contentCode=e.contentCode,this.subs.unsubscribe(),this.subs=new y;let t=this.i18nService.language$.subscribe(()=>{this.loadCollectionSummary()});this.subs.add(t)})}loadCollectionSummary(){this.loadingService.show(),this.#e.getCollectionTreeAndArticleTitlesByContentCode(this.contentCode).subscribe({next:e=>{this.files=e.map(t=>this.buildTreeNode(t)),this.loadingService.hide()},error:e=>{console.error("\u83B7\u53D6\u6587\u7AE0\u6811\u6570\u636E\u5931\u8D25:",e),this.loadingService.hide()}})}buildTreeNode(e){return{key:e.id,label:e.name||e.title,data:e,children:e.articles?e.articles.map(t=>this.buildTreeNode(t)):[]}}loadArticleDetail(e){e.key&&(this.loadingService.show(),this.#t.getArticleAggregate(+e.key).subscribe({next:t=>{this.articleDetail.set(t),this.extractTocFromContent(),this.loadingService.hide()},error:t=>{console.error("\u83B7\u53D6\u6587\u7AE0\u8BE6\u60C5\u5931\u8D25:",t),this.loadingService.hide()}}))}extractTocFromContent(){let e=this.articleDetail()?.content;if(!e){this.tocItems.set([]),this.articleContent.set("");return}let t=document.createElement("div");t.innerHTML=e;let i=t.querySelectorAll('a[href^="#"]'),a=[];i.forEach(u=>{let m=u.getAttribute("href"),s=u.textContent?.trim();if(m&&s){let h=m.substring(1);a.push({href:m,text:s,anchorId:h})}});let l=t.querySelector('a[name^="_"]');if(l?.parentElement){let u=l.parentElement,m=u.parentNode;if(m){let s=u.previousSibling;for(;s;){let h=s;s=s.previousSibling,m.removeChild(h)}}}let te=a.filter((u,m,s)=>m===s.findIndex(h=>h.anchorId===u.anchorId)),ie=this.sanitizer.bypassSecurityTrustHtml(t.innerHTML);this.articleContent.set(ie),this.tocItems.set(te)}onTocItemClick(e){this.scrollToAnchor(e.anchorId.trim())}scrollToAnchor(e){let t=this.elementRef.nativeElement.querySelector(".articaldetail-container");if(!t)return;let i=t.querySelector(`a[name="${e}"]`);i?i.scrollIntoView({behavior:"smooth",block:"start",inline:"nearest"}):console.warn(`\u672A\u627E\u5230\u951A\u70B9: ${e}`)}trackByTocItem(e,t){return t.anchorId}ngOnDestroy(){this.subs.unsubscribe()}static{this.\u0275fac=function(t){return new(t||n)}}static{this.\u0275cmp=k({type:n,selectors:[["app-collection-artical-tree"]],decls:17,vars:11,consts:[[1,"flex","flex-1"],["styleClass","w-[20rem] h-full","selectionMode","single","virtualScrollItemSize","36",3,"selectionChange","onNodeSelect","value","selection","virtualScroll","filter"],[1,"articaldetail-container","prose","max-w-none","p-6","flex-1",2,"overflow-y","auto","max-height","calc(100vh - 60px)"],[1,"text-3xl","font-bold","mb-4"],[1,"text-sm","mb-4","flex","items-center","gap-2"],[1,"mb-4","articaldetail-container",3,"innerHTML"],[1,"p-6","w-[20rem]","border-l-2"],[1,"flex","flex-col","max-h-[60vh]","overflow-y-auto"],[1,"text-lg","font-semibold","mb-4"],["class","text-sm italic",4,"ngIf"],["class","mt-2 cursor-pointer hover:text-primary-600 underline",3,"click",4,"ngFor","ngForOf","ngForTrackBy"],[1,"mt-6"],[1,"pi","pi-clock"],[1,"text-sm","italic"],[1,"mt-2","cursor-pointer","hover:text-primary-600","underline",3,"click"],[1,"flex","justify-between","items-center","mt-3"],[1,"pi","pi-cloud-download"]],template:function(t,i){if(t&1&&(c(0,"div",0)(1,"p-tree",1),L("selectionChange",function(l){return O(i.selectedFile,l)||(i.selectedFile=l),l}),_("onNodeSelect",function(l){return i.loadArticleDetail(l.node)}),r(),c(2,"div",2)(3,"h1",3),p(4),r(),c(5,"p",4),v(6,ne,3,4),r(),C(7,"div",5),r(),c(8,"div",6)(9,"div")(10,"div",7)(11,"h3",8),p(12,"\u76EE\u5F55"),r(),v(13,re,2,0,"div",9)(14,oe,2,1,"a",10),r(),c(15,"div",11),v(16,ce,4,0),r()()()()),t&2){let a;o(),f("value",i.files),R("selection",i.selectedFile),f("virtualScroll",!0)("filter",!0),o(3),S((a=i.articleDetail())==null?null:a.title),o(2),x(i.articleDetail()?6:-1),o(),f("innerHTML",i.articleContent()||"<p class='text-sm italic'>\u6682\u65E0\u5185\u5BB9</p>",D),o(6),f("ngIf",i.tocItems().length===0),o(),f("ngForOf",i.tocItems())("ngForTrackBy",i.trackByTocItem),o(2),x(i.notImageArticleFiles().length>0?16:-1)}},dependencies:[j,z,V,$,Z,Y,X],styles:["[_nghost-%COMP%]{flex:1;display:flex}[_nghost-%COMP%]     .p-virtualscroller{height:calc(100% - 30px)!important}"]})}};export{ee as CollectionArticleTreeComponent};
