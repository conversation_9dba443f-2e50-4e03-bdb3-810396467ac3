import{a as j,b as W,c as q}from"./chunk-YN5RRTSU.js";import"./chunk-2QGORWDL.js";import{a as V}from"./chunk-QJAYFQIR.js";import{a as U}from"./chunk-MJMCW5UB.js";import{a as N}from"./chunk-Y2PVWY7X.js";import"./chunk-MMC3E6BY.js";import"./chunk-KB23BJ64.js";import"./chunk-QAUHHFOT.js";import"./chunk-VBHJ4LBO.js";import{a as L,b as z}from"./chunk-QPTTFFWZ.js";import{c as E,d as F}from"./chunk-4JDMXTQG.js";import"./chunk-XXVSVAEK.js";import"./chunk-P4BOZY2U.js";import{d as $}from"./chunk-FUCZYBK4.js";import"./chunk-OTT6DUE3.js";import"./chunk-22JWGO27.js";import{ba as x,ua as A}from"./chunk-SXMRENJM.js";import"./chunk-BMA7WWEI.js";import{D as H,G as D,i as d,k}from"./chunk-GDGXRFMB.js";import{Ab as m,Bb as f,Fb as w,Kb as c,Lb as h,Oa as g,Ra as _,Ta as o,Vb as R,Xb as T,Z as n,a as u,ac as B,cb as M,fa as C,ga as b,ib as v,kc as I,mc as O,qb as s,ta as y,xb as S,yb as P,zb as a}from"./chunk-YUW2MUHJ.js";import"./chunk-EQDQRRRY.js";var J=(r,e)=>e.id;function K(r,e){if(r&1&&(a(0,"div",9),R(1),I(2,"date"),m()),r&2){let t=h().$implicit;o(),T(" ",O(2,1,t.creationTime,"yyyy-MM-dd HH:mm:ss")," ")}}function Q(r,e){if(r&1){let t=w();a(0,"p-card",3)(1,"p",6),c("click",function(){let p=C(t).$implicit,l=h();return b(l.navigateToArticle(p))}),m(),f(2,"p",7),v(3,K,3,4,"ng-template",8),m()}if(r&2){let t=e.$implicit;s("id",t.id),o(),s("innerHTML",t.title,g),o(),s("innerHTML",t.description||"<span class='italic'>\u6682\u65E0\u5185\u5BB9</span>",g)}}var G=class r{constructor(){this.home={icon:"pi pi-home",routerLink:"/"};this.breadcrumbItems=y([]);this.name="";this.totalRecords=50;this.rows=10;this.first=0;this._isMobile=!1;this.selectedDate=new Date;this.contentCode=null;this.cardItems=[];this.#e=n(V);this.#t=n(H);this.router=n(D);this.i18nService=n(N);this.subs=new u;this.loadingService=n(U);this.checkMobile()}#e;#t;ngOnInit(){this.#t.queryParams.subscribe(e=>{if(!e.contentCode)return;this.contentCode=e.contentCode,this.subs.unsubscribe(),this.subs=new u;let t=this.i18nService.language$.subscribe(()=>{this.loadCollectionSummary()});this.subs.add(t)})}loadCollectionSummary(){this.contentCode&&(this.loadingService.show(),this.#e.getCollectionSummaryByContentCode(this.contentCode,{skip:0,maxResultCount:10}).subscribe({next:e=>{this.name=e.name||"",this.cardItems=e.articles,this.totalRecords=e.totalRecords,this.initBreadcrumb(),this.loadingService.hide()},error:e=>{console.error("\u83B7\u53D6\u6458\u8981\u6570\u636E\u5931\u8D25:",e),this.loadingService.hide()}}))}initBreadcrumb(){let e=[{label:this.name}];this.breadcrumbItems.set(e)}navigateToArticle(e){e.id&&this.router.navigateByUrl(`/home/<USER>/${e.id}`)}get isMobile(){return this._isMobile}get rowsPerPageOptions(){return this.isMobile?void 0:[10,20,50]}onResize(e){this.checkMobile()}checkMobile(){this._isMobile=window.innerWidth<=768}onPageChange(e){this.first=e.first,this.rows=e.rows,console.log("\u9875\u9762\u53D8\u5316:",e)}onDateChange(e){console.log("\u9009\u62E9\u7684\u65E5\u671F:",e)}ngOnDestroy(){this.subs.unsubscribe()}static{this.\u0275fac=function(t){return new(t||r)}}static{this.\u0275cmp=M({type:r,selectors:[["app-summary-cards"]],hostBindings:function(t,i){t&1&&c("resize",function(l){return i.onResize(l)},!1,_)},features:[B([d])],decls:7,vars:8,consts:[[1,"summary-cards-container","p-6"],[3,"model","home"],[1,"grid","grid-cols-1","md:grid-cols-2","lg:grid-cols-3","gap-6","mb-8"],["styleClass","card-item",3,"id"],[1,"pagination-container"],["styleClass","custom-paginator",3,"onPageChange","first","rows","totalRecords","rowsPerPageOptions","showPageLinks","showCurrentPageReport"],[1,"p-card-title","cursor-pointer",3,"click","innerHTML"],[1,"mt-2","text-gray-500","text-sm",3,"innerHTML"],["pTemplate","footer"],[1,"text-gray-500","text-sm","flex","items-center"]],template:function(t,i){t&1&&(a(0,"div",0),f(1,"p-breadcrumb",1),a(2,"div",2),S(3,Q,4,3,"p-card",3,J),m(),a(5,"div",4)(6,"p-paginator",5),c("onPageChange",function(l){return i.onPageChange(l)}),m()()()),t&2&&(o(),s("model",i.breadcrumbItems())("home",i.home),o(2),P(i.cardItems),o(3),s("first",i.first)("rows",i.rows)("totalRecords",i.totalRecords)("rowsPerPageOptions",i.rowsPerPageOptions)("showPageLinks",!i.isMobile)("showCurrentPageReport",i.isMobile))},dependencies:[k,d,z,L,x,F,E,j,$,A,q,W],styles:["[_nghost-%COMP%]{flex:1}[_nghost-%COMP%]     .p-card{height:100%}.pagination-container[_ngcontent-%COMP%]{display:flex;justify-content:center;margin-top:2rem}.pagination-container[_ngcontent-%COMP%]   .custom-paginator[_ngcontent-%COMP%]{border:none;background:transparent}"]})}};export{G as SummaryCardsComponent};
