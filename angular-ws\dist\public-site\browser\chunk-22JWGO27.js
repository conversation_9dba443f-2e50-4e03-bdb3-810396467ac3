import{C as q,D as _t,G as Bt,K as Rt,W as mt,ba as At,ca as it,ga as N,ja as O,ka as nt,q as Wt,sa as kt,v as Q}from"./chunk-SXMRENJM.js";import{a as Mt,c as Ot,d as Pt,e as Dt,f as $t,g as Ht,k as et,l as rt}from"./chunk-GDGXRFMB.js";import{Ab as L,Bb as M,Cb as W,Db as B,Eb as X,Fb as Ft,Kb as pt,Lb as I,Mb as Y,Nb as J,Qb as K,Rb as yt,Sb as R,T as D,Ta as w,Tb as A,U as $,Ub as ot,Ya as ht,Z as T,ac as k,bc as Lt,cb as z,cc as tt,db as H,dc as st,ea as Ct,eb as ut,ec as Vt,fa as vt,ga as St,gb as v,ha as G,ia as b,ib as x,oa as dt,oc as lt,pa as It,pb as S,qb as _,rb as Tt,sa as xt,sb as U,tb as zt,ub as E,xa as Et,xc as ft,zb as F}from"./chunk-YUW2MUHJ.js";import{a as ct,b as gt}from"./chunk-EQDQRRRY.js";var Yt=({dt:n})=>`
.p-inputtext {
    font-family: inherit;
    font-feature-settings: inherit;
    font-size: 1rem;
    color: ${n("inputtext.color")};
    background: ${n("inputtext.background")};
    padding-block: ${n("inputtext.padding.y")};
    padding-inline: ${n("inputtext.padding.x")};
    border: 1px solid ${n("inputtext.border.color")};
    transition: background ${n("inputtext.transition.duration")}, color ${n("inputtext.transition.duration")}, border-color ${n("inputtext.transition.duration")}, outline-color ${n("inputtext.transition.duration")}, box-shadow ${n("inputtext.transition.duration")};
    appearance: none;
    border-radius: ${n("inputtext.border.radius")};
    outline-color: transparent;
    box-shadow: ${n("inputtext.shadow")};
}

.p-inputtext.ng-invalid.ng-dirty {
    border-color: ${n("inputtext.invalid.border.color")};
}

.p-inputtext:enabled:hover {
    border-color: ${n("inputtext.hover.border.color")};
}

.p-inputtext:enabled:focus {
    border-color: ${n("inputtext.focus.border.color")};
    box-shadow: ${n("inputtext.focus.ring.shadow")};
    outline: ${n("inputtext.focus.ring.width")} ${n("inputtext.focus.ring.style")} ${n("inputtext.focus.ring.color")};
    outline-offset: ${n("inputtext.focus.ring.offset")};
}

.p-inputtext.p-invalid {
    border-color: ${n("inputtext.invalid.border.color")};
}

.p-inputtext.p-variant-filled {
    background: ${n("inputtext.filled.background")};
}
    
.p-inputtext.p-variant-filled:enabled:hover {
    background: ${n("inputtext.filled.hover.background")};
}

.p-inputtext.p-variant-filled:enabled:focus {
    background: ${n("inputtext.filled.focus.background")};
}

.p-inputtext:disabled {
    opacity: 1;
    background: ${n("inputtext.disabled.background")};
    color: ${n("inputtext.disabled.color")};
}

.p-inputtext::placeholder {
    color: ${n("inputtext.placeholder.color")};
}

.p-inputtext.ng-invalid.ng-dirty::placeholder {
    color: ${n("inputtext.invalid.placeholder.color")};
}

.p-inputtext-sm {
    font-size: ${n("inputtext.sm.font.size")};
    padding-block: ${n("inputtext.sm.padding.y")};
    padding-inline: ${n("inputtext.sm.padding.x")};
}

.p-inputtext-lg {
    font-size: ${n("inputtext.lg.font.size")};
    padding-block: ${n("inputtext.lg.padding.y")};
    padding-inline: ${n("inputtext.lg.padding.x")};
}

.p-inputtext-fluid {
    width: 100%;
}
`,Jt={root:({instance:n,props:a})=>["p-inputtext p-component",{"p-filled":n.filled,"p-inputtext-sm":a.size==="small","p-inputtext-lg":a.size==="large","p-invalid":a.invalid,"p-variant-filled":a.variant==="filled","p-inputtext-fluid":a.fluid}]},Nt=(()=>{class n extends N{name="inputtext";theme=Yt;classes=Jt;static \u0275fac=(()=>{let t;return function(i){return(t||(t=b(n)))(i||n)}})();static \u0275prov=D({token:n,factory:n.\u0275fac})}return n})();var qe=(()=>{class n extends O{ngModel;variant;fluid;pSize;filled;_componentStyle=T(Nt);get hasFluid(){let e=this.el.nativeElement.closest("p-fluid");return Rt(this.fluid)?!!e:this.fluid}constructor(t){super(),this.ngModel=t}ngAfterViewInit(){super.ngAfterViewInit(),this.updateFilledState(),this.cd.detectChanges()}ngDoCheck(){this.updateFilledState()}onInput(){this.updateFilledState()}updateFilledState(){this.filled=this.el.nativeElement.value&&this.el.nativeElement.value.length||this.ngModel&&this.ngModel.model}static \u0275fac=function(e){return new(e||n)(ht(kt,8))};static \u0275dir=ut({type:n,selectors:[["","pInputText",""]],hostAttrs:[1,"p-inputtext","p-component"],hostVars:14,hostBindings:function(e,i){if(e&1&&pt("input",function(s){return i.onInput(s)}),e&2){let o;U("p-filled",i.filled)("p-variant-filled",((o=i.variant)!==null&&o!==void 0?o:i.config.inputStyle()||i.config.inputVariant())==="filled")("p-inputtext-fluid",i.hasFluid)("p-inputtext-sm",i.pSize==="small")("p-inputfield-sm",i.pSize==="small")("p-inputtext-lg",i.pSize==="large")("p-inputfield-lg",i.pSize==="large")}},inputs:{variant:"variant",fluid:[2,"fluid","fluid",ft],pSize:"pSize"},features:[k([Nt]),v]})}return n})(),Ze=(()=>{class n{static \u0275fac=function(e){return new(e||n)};static \u0275mod=H({type:n});static \u0275inj=$({})}return n})();var Xe=(()=>{class n extends nt{static \u0275fac=(()=>{let t;return function(i){return(t||(t=b(n)))(i||n)}})();static \u0275cmp=z({type:n,selectors:[["CheckIcon"]],features:[v],decls:2,vars:5,consts:[["width","14","height","14","viewBox","0 0 14 14","fill","none","xmlns","http://www.w3.org/2000/svg"],["d","M4.86199 11.5948C4.78717 11.5923 4.71366 11.5745 4.64596 11.5426C4.57826 11.5107 4.51779 11.4652 4.46827 11.4091L0.753985 7.69483C0.683167 7.64891 0.623706 7.58751 0.580092 7.51525C0.536478 7.44299 0.509851 7.36177 0.502221 7.27771C0.49459 7.19366 0.506156 7.10897 0.536046 7.03004C0.565935 6.95111 0.613367 6.88 0.674759 6.82208C0.736151 6.76416 0.8099 6.72095 0.890436 6.69571C0.970973 6.67046 1.05619 6.66385 1.13966 6.67635C1.22313 6.68886 1.30266 6.72017 1.37226 6.76792C1.44186 6.81567 1.4997 6.8786 1.54141 6.95197L4.86199 10.2503L12.6397 2.49483C12.7444 2.42694 12.8689 2.39617 12.9932 2.40745C13.1174 2.41873 13.2343 2.47141 13.3251 2.55705C13.4159 2.64268 13.4753 2.75632 13.4938 2.87973C13.5123 3.00315 13.4888 3.1292 13.4271 3.23768L5.2557 11.4091C5.20618 11.4652 5.14571 11.5107 5.07801 11.5426C5.01031 11.5745 4.9368 11.5923 4.86199 11.5948Z","fill","currentColor"]],template:function(e,i){e&1&&(G(),F(0,"svg",0),M(1,"path",1),L()),e&2&&(E(i.getClassNames()),S("aria-label",i.ariaLabel)("aria-hidden",i.ariaHidden)("role",i.role))},encapsulation:2})}return n})();var ti=(()=>{class n extends nt{pathId;ngOnInit(){this.pathId="url(#"+mt()+")"}static \u0275fac=(()=>{let t;return function(i){return(t||(t=b(n)))(i||n)}})();static \u0275cmp=z({type:n,selectors:[["SearchIcon"]],features:[v],decls:6,vars:7,consts:[["width","14","height","14","viewBox","0 0 14 14","fill","none","xmlns","http://www.w3.org/2000/svg"],["fill-rule","evenodd","clip-rule","evenodd","d","M2.67602 11.0265C3.6661 11.688 4.83011 12.0411 6.02086 12.0411C6.81149 12.0411 7.59438 11.8854 8.32483 11.5828C8.87005 11.357 9.37808 11.0526 9.83317 10.6803L12.9769 13.8241C13.0323 13.8801 13.0983 13.9245 13.171 13.9548C13.2438 13.985 13.3219 14.0003 13.4007 14C13.4795 14.0003 13.5575 13.985 13.6303 13.9548C13.7031 13.9245 13.7691 13.8801 13.8244 13.8241C13.9367 13.7116 13.9998 13.5592 13.9998 13.4003C13.9998 13.2414 13.9367 13.089 13.8244 12.9765L10.6807 9.8328C11.053 9.37773 11.3573 8.86972 11.5831 8.32452C11.8857 7.59408 12.0414 6.81119 12.0414 6.02056C12.0414 4.8298 11.6883 3.66579 11.0268 2.67572C10.3652 1.68564 9.42494 0.913972 8.32483 0.45829C7.22472 0.00260857 6.01418 -0.116618 4.84631 0.115686C3.67844 0.34799 2.60568 0.921393 1.76369 1.76338C0.921698 2.60537 0.348296 3.67813 0.115991 4.84601C-0.116313 6.01388 0.00291375 7.22441 0.458595 8.32452C0.914277 9.42464 1.68595 10.3649 2.67602 11.0265ZM3.35565 2.0158C4.14456 1.48867 5.07206 1.20731 6.02086 1.20731C7.29317 1.20731 8.51338 1.71274 9.41304 2.6124C10.3127 3.51206 10.8181 4.73226 10.8181 6.00457C10.8181 6.95337 10.5368 7.88088 10.0096 8.66978C9.48251 9.45868 8.73328 10.0736 7.85669 10.4367C6.98011 10.7997 6.01554 10.8947 5.08496 10.7096C4.15439 10.5245 3.2996 10.0676 2.62869 9.39674C1.95778 8.72583 1.50089 7.87104 1.31579 6.94046C1.13068 6.00989 1.22568 5.04532 1.58878 4.16874C1.95187 3.29215 2.56675 2.54292 3.35565 2.0158Z","fill","currentColor"],[3,"id"],["width","14","height","14","fill","white"]],template:function(e,i){e&1&&(G(),F(0,"svg",0)(1,"g"),M(2,"path",1),L(),F(3,"defs")(4,"clipPath",2),M(5,"rect",3),L()()()),e&2&&(E(i.getClassNames()),S("aria-label",i.ariaLabel)("aria-hidden",i.ariaHidden)("role",i.role),w(),S("clip-path",i.pathId),w(3),_("id",i.pathId))},encapsulation:2})}return n})();var jt=(()=>{class n extends nt{pathId;ngOnInit(){this.pathId="url(#"+mt()+")"}static \u0275fac=(()=>{let t;return function(i){return(t||(t=b(n)))(i||n)}})();static \u0275cmp=z({type:n,selectors:[["SpinnerIcon"]],features:[v],decls:6,vars:7,consts:[["width","14","height","14","viewBox","0 0 14 14","fill","none","xmlns","http://www.w3.org/2000/svg"],["d","M6.99701 14C5.85441 13.999 4.72939 13.7186 3.72012 13.1832C2.71084 12.6478 1.84795 11.8737 1.20673 10.9284C0.565504 9.98305 0.165424 8.89526 0.041387 7.75989C-0.0826496 6.62453 0.073125 5.47607 0.495122 4.4147C0.917119 3.35333 1.59252 2.4113 2.46241 1.67077C3.33229 0.930247 4.37024 0.413729 5.4857 0.166275C6.60117 -0.0811796 7.76026 -0.0520535 8.86188 0.251112C9.9635 0.554278 10.9742 1.12227 11.8057 1.90555C11.915 2.01493 11.9764 2.16319 11.9764 2.31778C11.9764 2.47236 11.915 2.62062 11.8057 2.73C11.7521 2.78503 11.688 2.82877 11.6171 2.85864C11.5463 2.8885 11.4702 2.90389 11.3933 2.90389C11.3165 2.90389 11.2404 2.8885 11.1695 2.85864C11.0987 2.82877 11.0346 2.78503 10.9809 2.73C9.9998 1.81273 8.73246 1.26138 7.39226 1.16876C6.05206 1.07615 4.72086 1.44794 3.62279 2.22152C2.52471 2.99511 1.72683 4.12325 1.36345 5.41602C1.00008 6.70879 1.09342 8.08723 1.62775 9.31926C2.16209 10.5513 3.10478 11.5617 4.29713 12.1803C5.48947 12.7989 6.85865 12.988 8.17414 12.7157C9.48963 12.4435 10.6711 11.7264 11.5196 10.6854C12.3681 9.64432 12.8319 8.34282 12.8328 7C12.8328 6.84529 12.8943 6.69692 13.0038 6.58752C13.1132 6.47812 13.2616 6.41667 13.4164 6.41667C13.5712 6.41667 13.7196 6.47812 13.8291 6.58752C13.9385 6.69692 14 6.84529 14 7C14 8.85651 13.2622 10.637 11.9489 11.9497C10.6356 13.2625 8.85432 14 6.99701 14Z","fill","currentColor"],[3,"id"],["width","14","height","14","fill","white"]],template:function(e,i){e&1&&(G(),F(0,"svg",0)(1,"g"),M(2,"path",1),L(),F(3,"defs")(4,"clipPath",2),M(5,"rect",3),L()()()),e&2&&(E(i.getClassNames()),S("aria-label",i.ariaLabel)("aria-hidden",i.ariaHidden)("role",i.role),w(),S("clip-path",i.pathId),w(3),_("id",i.pathId))},encapsulation:2})}return n})();var wt=(()=>{class n{static zindex=1e3;static calculatedScrollbarWidth=null;static calculatedScrollbarHeight=null;static browser;static addClass(t,e){t&&e&&(t.classList?t.classList.add(e):t.className+=" "+e)}static addMultipleClasses(t,e){if(t&&e)if(t.classList){let i=e.trim().split(" ");for(let o=0;o<i.length;o++)t.classList.add(i[o])}else{let i=e.split(" ");for(let o=0;o<i.length;o++)t.className+=" "+i[o]}}static removeClass(t,e){t&&e&&(t.classList?t.classList.remove(e):t.className=t.className.replace(new RegExp("(^|\\b)"+e.split(" ").join("|")+"(\\b|$)","gi")," "))}static removeMultipleClasses(t,e){t&&e&&[e].flat().filter(Boolean).forEach(i=>i.split(" ").forEach(o=>this.removeClass(t,o)))}static hasClass(t,e){return t&&e?t.classList?t.classList.contains(e):new RegExp("(^| )"+e+"( |$)","gi").test(t.className):!1}static siblings(t){return Array.prototype.filter.call(t.parentNode.children,function(e){return e!==t})}static find(t,e){return Array.from(t.querySelectorAll(e))}static findSingle(t,e){return this.isElement(t)?t.querySelector(e):null}static index(t){let e=t.parentNode.childNodes,i=0;for(var o=0;o<e.length;o++){if(e[o]==t)return i;e[o].nodeType==1&&i++}return-1}static indexWithinGroup(t,e){let i=t.parentNode?t.parentNode.childNodes:[],o=0;for(var s=0;s<i.length;s++){if(i[s]==t)return o;i[s].attributes&&i[s].attributes[e]&&i[s].nodeType==1&&o++}return-1}static appendOverlay(t,e,i="self"){i!=="self"&&t&&e&&this.appendChild(t,e)}static alignOverlay(t,e,i="self",o=!0){t&&e&&(o&&(t.style.minWidth=`${n.getOuterWidth(e)}px`),i==="self"?this.relativePosition(t,e):this.absolutePosition(t,e))}static relativePosition(t,e,i=!0){let o=m=>{if(m)return getComputedStyle(m).getPropertyValue("position")==="relative"?m:o(m.parentElement)},s=t.offsetParent?{width:t.offsetWidth,height:t.offsetHeight}:this.getHiddenElementDimensions(t),l=e.offsetHeight,r=e.getBoundingClientRect(),d=this.getWindowScrollTop(),c=this.getWindowScrollLeft(),p=this.getViewport(),f=o(t)?.getBoundingClientRect()||{top:-1*d,left:-1*c},y,C;r.top+l+s.height>p.height?(y=r.top-f.top-s.height,t.style.transformOrigin="bottom",r.top+y<0&&(y=-1*r.top)):(y=l+r.top-f.top,t.style.transformOrigin="top");let h=r.left+s.width-p.width,u=r.left-f.left;s.width>p.width?C=(r.left-f.left)*-1:h>0?C=u-h:C=r.left-f.left,t.style.top=y+"px",t.style.left=C+"px",i&&(t.style.marginTop=origin==="bottom"?"calc(var(--p-anchor-gutter) * -1)":"calc(var(--p-anchor-gutter))")}static absolutePosition(t,e,i=!0){let o=t.offsetParent?{width:t.offsetWidth,height:t.offsetHeight}:this.getHiddenElementDimensions(t),s=o.height,l=o.width,r=e.offsetHeight,d=e.offsetWidth,c=e.getBoundingClientRect(),p=this.getWindowScrollTop(),g=this.getWindowScrollLeft(),f=this.getViewport(),y,C;c.top+r+s>f.height?(y=c.top+p-s,t.style.transformOrigin="bottom",y<0&&(y=p)):(y=r+c.top+p,t.style.transformOrigin="top"),c.left+l>f.width?C=Math.max(0,c.left+g+d-l):C=c.left+g,t.style.top=y+"px",t.style.left=C+"px",i&&(t.style.marginTop=origin==="bottom"?"calc(var(--p-anchor-gutter) * -1)":"calc(var(--p-anchor-gutter))")}static getParents(t,e=[]){return t.parentNode===null?e:this.getParents(t.parentNode,e.concat([t.parentNode]))}static getScrollableParents(t){let e=[];if(t){let i=this.getParents(t),o=/(auto|scroll)/,s=l=>{let r=window.getComputedStyle(l,null);return o.test(r.getPropertyValue("overflow"))||o.test(r.getPropertyValue("overflowX"))||o.test(r.getPropertyValue("overflowY"))};for(let l of i){let r=l.nodeType===1&&l.dataset.scrollselectors;if(r){let d=r.split(",");for(let c of d){let p=this.findSingle(l,c);p&&s(p)&&e.push(p)}}l.nodeType!==9&&s(l)&&e.push(l)}}return e}static getHiddenElementOuterHeight(t){t.style.visibility="hidden",t.style.display="block";let e=t.offsetHeight;return t.style.display="none",t.style.visibility="visible",e}static getHiddenElementOuterWidth(t){t.style.visibility="hidden",t.style.display="block";let e=t.offsetWidth;return t.style.display="none",t.style.visibility="visible",e}static getHiddenElementDimensions(t){let e={};return t.style.visibility="hidden",t.style.display="block",e.width=t.offsetWidth,e.height=t.offsetHeight,t.style.display="none",t.style.visibility="visible",e}static scrollInView(t,e){let i=getComputedStyle(t).getPropertyValue("borderTopWidth"),o=i?parseFloat(i):0,s=getComputedStyle(t).getPropertyValue("paddingTop"),l=s?parseFloat(s):0,r=t.getBoundingClientRect(),c=e.getBoundingClientRect().top+document.body.scrollTop-(r.top+document.body.scrollTop)-o-l,p=t.scrollTop,g=t.clientHeight,f=this.getOuterHeight(e);c<0?t.scrollTop=p+c:c+f>g&&(t.scrollTop=p+c-g+f)}static fadeIn(t,e){t.style.opacity=0;let i=+new Date,o=0,s=function(){o=+t.style.opacity.replace(",",".")+(new Date().getTime()-i)/e,t.style.opacity=o,i=+new Date,+o<1&&(window.requestAnimationFrame&&requestAnimationFrame(s)||setTimeout(s,16))};s()}static fadeOut(t,e){var i=1,o=50,s=e,l=o/s;let r=setInterval(()=>{i=i-l,i<=0&&(i=0,clearInterval(r)),t.style.opacity=i},o)}static getWindowScrollTop(){let t=document.documentElement;return(window.pageYOffset||t.scrollTop)-(t.clientTop||0)}static getWindowScrollLeft(){let t=document.documentElement;return(window.pageXOffset||t.scrollLeft)-(t.clientLeft||0)}static matches(t,e){var i=Element.prototype,o=i.matches||i.webkitMatchesSelector||i.mozMatchesSelector||i.msMatchesSelector||function(s){return[].indexOf.call(document.querySelectorAll(s),this)!==-1};return o.call(t,e)}static getOuterWidth(t,e){let i=t.offsetWidth;if(e){let o=getComputedStyle(t);i+=parseFloat(o.marginLeft)+parseFloat(o.marginRight)}return i}static getHorizontalPadding(t){let e=getComputedStyle(t);return parseFloat(e.paddingLeft)+parseFloat(e.paddingRight)}static getHorizontalMargin(t){let e=getComputedStyle(t);return parseFloat(e.marginLeft)+parseFloat(e.marginRight)}static innerWidth(t){let e=t.offsetWidth,i=getComputedStyle(t);return e+=parseFloat(i.paddingLeft)+parseFloat(i.paddingRight),e}static width(t){let e=t.offsetWidth,i=getComputedStyle(t);return e-=parseFloat(i.paddingLeft)+parseFloat(i.paddingRight),e}static getInnerHeight(t){let e=t.offsetHeight,i=getComputedStyle(t);return e+=parseFloat(i.paddingTop)+parseFloat(i.paddingBottom),e}static getOuterHeight(t,e){let i=t.offsetHeight;if(e){let o=getComputedStyle(t);i+=parseFloat(o.marginTop)+parseFloat(o.marginBottom)}return i}static getHeight(t){let e=t.offsetHeight,i=getComputedStyle(t);return e-=parseFloat(i.paddingTop)+parseFloat(i.paddingBottom)+parseFloat(i.borderTopWidth)+parseFloat(i.borderBottomWidth),e}static getWidth(t){let e=t.offsetWidth,i=getComputedStyle(t);return e-=parseFloat(i.paddingLeft)+parseFloat(i.paddingRight)+parseFloat(i.borderLeftWidth)+parseFloat(i.borderRightWidth),e}static getViewport(){let t=window,e=document,i=e.documentElement,o=e.getElementsByTagName("body")[0],s=t.innerWidth||i.clientWidth||o.clientWidth,l=t.innerHeight||i.clientHeight||o.clientHeight;return{width:s,height:l}}static getOffset(t){var e=t.getBoundingClientRect();return{top:e.top+(window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0),left:e.left+(window.pageXOffset||document.documentElement.scrollLeft||document.body.scrollLeft||0)}}static replaceElementWith(t,e){let i=t.parentNode;if(!i)throw"Can't replace element";return i.replaceChild(e,t)}static getUserAgent(){if(navigator&&this.isClient())return navigator.userAgent}static isIE(){var t=window.navigator.userAgent,e=t.indexOf("MSIE ");if(e>0)return!0;var i=t.indexOf("Trident/");if(i>0){var o=t.indexOf("rv:");return!0}var s=t.indexOf("Edge/");return s>0}static isIOS(){return/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream}static isAndroid(){return/(android)/i.test(navigator.userAgent)}static isTouchDevice(){return"ontouchstart"in window||navigator.maxTouchPoints>0}static appendChild(t,e){if(this.isElement(e))e.appendChild(t);else if(e&&e.el&&e.el.nativeElement)e.el.nativeElement.appendChild(t);else throw"Cannot append "+e+" to "+t}static removeChild(t,e){if(this.isElement(e))e.removeChild(t);else if(e.el&&e.el.nativeElement)e.el.nativeElement.removeChild(t);else throw"Cannot remove "+t+" from "+e}static removeElement(t){"remove"in Element.prototype?t.remove():t.parentNode.removeChild(t)}static isElement(t){return typeof HTMLElement=="object"?t instanceof HTMLElement:t&&typeof t=="object"&&t!==null&&t.nodeType===1&&typeof t.nodeName=="string"}static calculateScrollbarWidth(t){if(t){let e=getComputedStyle(t);return t.offsetWidth-t.clientWidth-parseFloat(e.borderLeftWidth)-parseFloat(e.borderRightWidth)}else{if(this.calculatedScrollbarWidth!==null)return this.calculatedScrollbarWidth;let e=document.createElement("div");e.className="p-scrollbar-measure",document.body.appendChild(e);let i=e.offsetWidth-e.clientWidth;return document.body.removeChild(e),this.calculatedScrollbarWidth=i,i}}static calculateScrollbarHeight(){if(this.calculatedScrollbarHeight!==null)return this.calculatedScrollbarHeight;let t=document.createElement("div");t.className="p-scrollbar-measure",document.body.appendChild(t);let e=t.offsetHeight-t.clientHeight;return document.body.removeChild(t),this.calculatedScrollbarWidth=e,e}static invokeElementMethod(t,e,i){t[e].apply(t,i)}static clearSelection(){if(window.getSelection)window.getSelection().empty?window.getSelection().empty():window.getSelection().removeAllRanges&&window.getSelection().rangeCount>0&&window.getSelection().getRangeAt(0).getClientRects().length>0&&window.getSelection().removeAllRanges();else if(document.selection&&document.selection.empty)try{document.selection.empty()}catch{}}static getBrowser(){if(!this.browser){let t=this.resolveUserAgent();this.browser={},t.browser&&(this.browser[t.browser]=!0,this.browser.version=t.version),this.browser.chrome?this.browser.webkit=!0:this.browser.webkit&&(this.browser.safari=!0)}return this.browser}static resolveUserAgent(){let t=navigator.userAgent.toLowerCase(),e=/(chrome)[ \/]([\w.]+)/.exec(t)||/(webkit)[ \/]([\w.]+)/.exec(t)||/(opera)(?:.*version|)[ \/]([\w.]+)/.exec(t)||/(msie) ([\w.]+)/.exec(t)||t.indexOf("compatible")<0&&/(mozilla)(?:.*? rv:([\w.]+)|)/.exec(t)||[];return{browser:e[1]||"",version:e[2]||"0"}}static isInteger(t){return Number.isInteger?Number.isInteger(t):typeof t=="number"&&isFinite(t)&&Math.floor(t)===t}static isHidden(t){return!t||t.offsetParent===null}static isVisible(t){return t&&t.offsetParent!=null}static isExist(t){return t!==null&&typeof t<"u"&&t.nodeName&&t.parentNode}static focus(t,e){t&&document.activeElement!==t&&t.focus(e)}static getFocusableSelectorString(t=""){return`button:not([tabindex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${t},
        [href][clientHeight][clientWidth]:not([tabindex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${t},
        input:not([tabindex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${t},
        select:not([tabindex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${t},
        textarea:not([tabindex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${t},
        [tabIndex]:not([tabIndex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${t},
        [contenteditable]:not([tabIndex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${t},
        .p-inputtext:not([tabindex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${t},
        .p-button:not([tabindex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${t}`}static getFocusableElements(t,e=""){let i=this.find(t,this.getFocusableSelectorString(e)),o=[];for(let s of i){let l=getComputedStyle(s);this.isVisible(s)&&l.display!="none"&&l.visibility!="hidden"&&o.push(s)}return o}static getFocusableElement(t,e=""){let i=this.findSingle(t,this.getFocusableSelectorString(e));if(i){let o=getComputedStyle(i);if(this.isVisible(i)&&o.display!="none"&&o.visibility!="hidden")return i}return null}static getFirstFocusableElement(t,e=""){let i=this.getFocusableElements(t,e);return i.length>0?i[0]:null}static getLastFocusableElement(t,e){let i=this.getFocusableElements(t,e);return i.length>0?i[i.length-1]:null}static getNextFocusableElement(t,e=!1){let i=n.getFocusableElements(t),o=0;if(i&&i.length>0){let s=i.indexOf(i[0].ownerDocument.activeElement);e?s==-1||s===0?o=i.length-1:o=s-1:s!=-1&&s!==i.length-1&&(o=s+1)}return i[o]}static generateZIndex(){return this.zindex=this.zindex||999,++this.zindex}static getSelection(){return window.getSelection?window.getSelection().toString():document.getSelection?document.getSelection().toString():document.selection?document.selection.createRange().text:null}static getTargetElement(t,e){if(!t)return null;switch(t){case"document":return document;case"window":return window;case"@next":return e?.nextElementSibling;case"@prev":return e?.previousElementSibling;case"@parent":return e?.parentElement;case"@grandparent":return e?.parentElement.parentElement;default:let i=typeof t;if(i==="string")return document.querySelector(t);if(i==="object"&&t.hasOwnProperty("nativeElement"))return this.isExist(t.nativeElement)?t.nativeElement:void 0;let s=(l=>!!(l&&l.constructor&&l.call&&l.apply))(t)?t():t;return s&&s.nodeType===9||this.isExist(s)?s:null}}static isClient(){return!!(typeof window<"u"&&window.document&&window.document.createElement)}static getAttribute(t,e){if(t){let i=t.getAttribute(e);return isNaN(i)?i==="true"||i==="false"?i==="true":i:+i}}static calculateBodyScrollbarWidth(){return window.innerWidth-document.documentElement.offsetWidth}static blockBodyScroll(t="p-overflow-hidden"){document.body.style.setProperty("--scrollbar-width",this.calculateBodyScrollbarWidth()+"px"),this.addClass(document.body,t)}static unblockBodyScroll(t="p-overflow-hidden"){document.body.style.removeProperty("--scrollbar-width"),this.removeClass(document.body,t)}static createElement(t,e={},...i){if(t){let o=document.createElement(t);return this.setAttributes(o,e),o.append(...i),o}}static setAttribute(t,e="",i){this.isElement(t)&&i!==null&&i!==void 0&&t.setAttribute(e,i)}static setAttributes(t,e={}){if(this.isElement(t)){let i=(o,s)=>{let l=t?.$attrs?.[o]?[t?.$attrs?.[o]]:[];return[s].flat().reduce((r,d)=>{if(d!=null){let c=typeof d;if(c==="string"||c==="number")r.push(d);else if(c==="object"){let p=Array.isArray(d)?i(o,d):Object.entries(d).map(([g,f])=>o==="style"&&(f||f===0)?`${g.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase()}:${f}`:f?g:void 0);r=p.length?r.concat(p.filter(g=>!!g)):r}}return r},l)};Object.entries(e).forEach(([o,s])=>{if(s!=null){let l=o.match(/^on(.+)/);l?t.addEventListener(l[1].toLowerCase(),s):o==="pBind"?this.setAttributes(t,s):(s=o==="class"?[...new Set(i("class",s))].join(" ").trim():o==="style"?i("style",s).join(";").trim():s,(t.$attrs=t.$attrs||{})&&(t.$attrs[o]=s),t.setAttribute(o,s))}})}}static isFocusableElement(t,e=""){return this.isElement(t)?t.matches(`button:not([tabindex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${e},
                [href][clientHeight][clientWidth]:not([tabindex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${e},
                input:not([tabindex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${e},
                select:not([tabindex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${e},
                textarea:not([tabindex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${e},
                [tabIndex]:not([tabIndex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${e},
                [contenteditable]:not([tabIndex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${e}`):!1}}return n})(),Qt=class{element;listener;scrollableParents;constructor(a,t=()=>{}){this.element=a,this.listener=t}bindScrollListener(){this.scrollableParents=wt.getScrollableParents(this.element);for(let a=0;a<this.scrollableParents.length;a++)this.scrollableParents[a].addEventListener("scroll",this.listener)}unbindScrollListener(){if(this.scrollableParents)for(let a=0;a<this.scrollableParents.length;a++)this.scrollableParents[a].removeEventListener("scroll",this.listener)}destroy(){this.unbindScrollListener(),this.element=null,this.listener=null,this.scrollableParents=null}};var pi=(()=>{class n extends O{autofocus=!1;_autofocus=!1;focused=!1;platformId=T(Et);document=T(Mt);host=T(xt);ngAfterContentChecked(){this.autofocus===!1?this.host.nativeElement.removeAttribute("autofocus"):this.host.nativeElement.setAttribute("autofocus",!0),this.focused||this.autoFocus()}ngAfterViewChecked(){this.focused||this.autoFocus()}autoFocus(){rt(this.platformId)&&this._autofocus&&setTimeout(()=>{let t=wt.getFocusableElements(this.host?.nativeElement);t.length===0&&this.host.nativeElement.focus(),t.length>0&&t[0].focus(),this.focused=!0})}static \u0275fac=(()=>{let t;return function(i){return(t||(t=b(n)))(i||n)}})();static \u0275dir=ut({type:n,selectors:[["","pAutoFocus",""]],inputs:{autofocus:[2,"autofocus","autofocus",ft],_autofocus:[0,"pAutoFocus","_autofocus"]},features:[v]})}return n})(),fi=(()=>{class n{static \u0275fac=function(e){return new(e||n)};static \u0275mod=H({type:n});static \u0275inj=$({})}return n})();var Kt=["*"],te=({dt:n})=>`
.p-iconfield {
    position: relative;
    display: block;
}

.p-inputicon {
    position: absolute;
    top: 50%;
    margin-top: calc(-1 * (${n("icon.size")} / 2));
    color: ${n("iconfield.icon.color")};
    line-height: 1;
}

.p-iconfield .p-inputicon:first-child {
    inset-inline-start: ${n("form.field.padding.x")};
}

.p-iconfield .p-inputicon:last-child {
    inset-inline-end: ${n("form.field.padding.x")};
}

.p-iconfield .p-inputtext:not(:first-child) {
    padding-inline-start: calc((${n("form.field.padding.x")} * 2) + ${n("icon.size")});
}

.p-iconfield .p-inputtext:not(:last-child) {
    padding-inline-end: calc((${n("form.field.padding.x")} * 2) + ${n("icon.size")});
}

.p-iconfield:has(.p-inputfield-sm) .p-inputicon {
    font-size: ${n("form.field.sm.font.size")};
    width: ${n("form.field.sm.font.size")};
    height: ${n("form.field.sm.font.size")};
    margin-top: calc(-1 * (${n("form.field.sm.font.size")} / 2));
}

.p-iconfield:has(.p-inputfield-lg) .p-inputicon {
    font-size: ${n("form.field.lg.font.size")};
    width: ${n("form.field.lg.font.size")};
    height: ${n("form.field.lg.font.size")};
    margin-top: calc(-1 * (${n("form.field.lg.font.size")} / 2));
}
`,ee={root:"p-iconfield"},qt=(()=>{class n extends N{name="iconfield";theme=te;classes=ee;static \u0275fac=(()=>{let t;return function(i){return(t||(t=b(n)))(i||n)}})();static \u0275prov=D({token:n,factory:n.\u0275fac})}return n})();var Ti=(()=>{class n extends O{iconPosition="left";get _styleClass(){return this.styleClass}styleClass;_componentStyle=T(qt);static \u0275fac=(()=>{let t;return function(i){return(t||(t=b(n)))(i||n)}})();static \u0275cmp=z({type:n,selectors:[["p-iconfield"],["p-iconField"],["p-icon-field"]],hostAttrs:[1,"p-iconfield"],hostVars:6,hostBindings:function(e,i){e&2&&(E(i._styleClass),U("p-iconfield-left",i.iconPosition==="left")("p-iconfield-right",i.iconPosition==="right"))},inputs:{iconPosition:"iconPosition",styleClass:"styleClass"},features:[k([qt]),v],ngContentSelectors:Kt,decls:1,vars:0,template:function(e,i){e&1&&(Y(),J(0))},dependencies:[et],encapsulation:2,changeDetection:0})}return n})();var ie=["*"],ne={root:"p-inputicon"},Zt=(()=>{class n extends N{name="inputicon";classes=ne;static \u0275fac=(()=>{let t;return function(i){return(t||(t=b(n)))(i||n)}})();static \u0275prov=D({token:n,factory:n.\u0275fac})}return n})(),Ai=(()=>{class n extends O{styleClass;get hostClasses(){return this.styleClass}_componentStyle=T(Zt);static \u0275fac=(()=>{let t;return function(i){return(t||(t=b(n)))(i||n)}})();static \u0275cmp=z({type:n,selectors:[["p-inputicon"],["p-inputIcon"]],hostVars:4,hostBindings:function(e,i){e&2&&(E(i.hostClasses),U("p-inputicon",!0))},inputs:{styleClass:"styleClass"},features:[k([Zt]),v],ngContentSelectors:ie,decls:1,vars:0,template:function(e,i){e&1&&(Y(),J(0))},dependencies:[et,it],encapsulation:2,changeDetection:0})}return n})();var Gt=["content"],oe=["item"],se=["loader"],le=["loadericon"],re=["element"],ae=["*"],ce=(n,a,t)=>({"p-virtualscroller":!0,"p-virtualscroller-inline":n,"p-virtualscroller-both p-both-scroll":a,"p-virtualscroller-horizontal p-horizontal-scroll":t}),bt=(n,a)=>({$implicit:n,options:a}),de=n=>({"p-virtualscroller-content":!0,"p-virtualscroller-loading ":n}),he=n=>({"p-virtualscroller-loader-mask":n}),ue=n=>({numCols:n}),Xt=n=>({options:n}),pe=()=>({styleClass:"p-virtualscroller-loading-icon"}),fe=(n,a)=>({rows:n,columns:a});function me(n,a){n&1&&X(0)}function ge(n,a){if(n&1&&(W(0),x(1,me,1,0,"ng-container",10),B()),n&2){let t=I(2);w(),_("ngTemplateOutlet",t.contentTemplate||t._contentTemplate)("ngTemplateOutletContext",st(2,bt,t.loadedItems,t.getContentOptions()))}}function ye(n,a){n&1&&X(0)}function _e(n,a){if(n&1&&(W(0),x(1,ye,1,0,"ng-container",10),B()),n&2){let t=a.$implicit,e=a.index,i=I(3);w(),_("ngTemplateOutlet",i.itemTemplate||i._itemTemplate)("ngTemplateOutletContext",st(2,bt,t,i.getOptions(e)))}}function we(n,a){if(n&1&&(F(0,"div",11,3),x(2,_e,2,5,"ng-container",12),L()),n&2){let t=I(2);zt(t.contentStyle),E(t.contentStyleClass),_("ngClass",tt(8,de,t.d_loading)),S("data-pc-section","content"),w(2),_("ngForOf",t.loadedItems)("ngForTrackBy",t._trackBy)}}function be(n,a){if(n&1&&M(0,"div",13),n&2){let t=I(2);_("ngStyle",t.spacerStyle),S("data-pc-section","spacer")}}function Ce(n,a){n&1&&X(0)}function ve(n,a){if(n&1&&(W(0),x(1,Ce,1,0,"ng-container",10),B()),n&2){let t=a.index,e=I(4);w(),_("ngTemplateOutlet",e.loaderTemplate||e._loaderTemplate)("ngTemplateOutletContext",tt(4,Xt,e.getLoaderOptions(t,e.both&&tt(2,ue,e.numItemsInViewport.cols))))}}function Se(n,a){if(n&1&&(W(0),x(1,ve,2,6,"ng-container",15),B()),n&2){let t=I(3);w(),_("ngForOf",t.loaderArr)}}function Ie(n,a){n&1&&X(0)}function xe(n,a){if(n&1&&(W(0),x(1,Ie,1,0,"ng-container",10),B()),n&2){let t=I(4);w(),_("ngTemplateOutlet",t.loaderIconTemplate||t._loaderIconTemplate)("ngTemplateOutletContext",tt(3,Xt,Lt(2,pe)))}}function Ee(n,a){n&1&&M(0,"SpinnerIcon",16),n&2&&(_("styleClass","p-virtualscroller-loading-icon pi-spin"),S("data-pc-section","loadingIcon"))}function Te(n,a){if(n&1&&x(0,xe,2,5,"ng-container",6)(1,Ee,1,2,"ng-template",null,5,lt),n&2){let t=ot(2),e=I(3);_("ngIf",e.loaderIconTemplate||e._loaderIconTemplate)("ngIfElse",t)}}function ze(n,a){if(n&1&&(F(0,"div",14),x(1,Se,2,1,"ng-container",6)(2,Te,3,2,"ng-template",null,4,lt),L()),n&2){let t=ot(3),e=I(2);_("ngClass",tt(4,he,!e.loaderTemplate)),S("data-pc-section","loader"),w(),_("ngIf",e.loaderTemplate||e._loaderTemplate)("ngIfElse",t)}}function Fe(n,a){if(n&1){let t=Ft();W(0),F(1,"div",7,1),pt("scroll",function(i){vt(t);let o=I();return St(o.onContainerScroll(i))}),x(3,ge,2,5,"ng-container",6)(4,we,3,10,"ng-template",null,2,lt)(6,be,1,2,"div",8)(7,ze,4,6,"div",9),L(),B()}if(n&2){let t=ot(5),e=I();w(),E(e._styleClass),_("ngStyle",e._style)("ngClass",Vt(12,ce,e.inline,e.both,e.horizontal)),S("id",e._id)("tabindex",e.tabindex)("data-pc-name","scroller")("data-pc-section","root"),w(2),_("ngIf",e.contentTemplate||e._contentTemplate)("ngIfElse",t),w(3),_("ngIf",e._showSpacer),w(),_("ngIf",!e.loaderDisabled&&e._showLoader&&e.d_loading)}}function Le(n,a){n&1&&X(0)}function Ve(n,a){if(n&1&&(W(0),x(1,Le,1,0,"ng-container",10),B()),n&2){let t=I(2);w(),_("ngTemplateOutlet",t.contentTemplate||t._contentTemplate)("ngTemplateOutletContext",st(5,bt,t.items,st(2,fe,t._items,t.loadedColumns)))}}function Me(n,a){if(n&1&&(J(0),x(1,Ve,2,8,"ng-container",17)),n&2){let t=I();w(),_("ngIf",t.contentTemplate||t._contentTemplate)}}var Oe=({dt:n})=>`
.p-virtualscroller {
    position: relative;
    overflow: auto;
    contain: strict;
    transform: translateZ(0);
    will-change: scroll-position;
    outline: 0 none;
}

.p-virtualscroller-content {
    position: absolute;
    top: 0;
    left: 0;
    min-height: 100%;
    min-width: 100%;
    will-change: transform;
}

.p-virtualscroller-spacer {
    position: absolute;
    top: 0;
    left: 0;
    height: 1px;
    width: 1px;
    transform-origin: 0 0;
    pointer-events: none;
}

.p-virtualscroller-loader {
    position: sticky;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: ${n("virtualscroller.loader.mask.background")};
    color: ${n("virtualscroller.loader.mask.color")};
}

.p-virtualscroller-loader-mask {
    display: flex;
    align-items: center;
    justify-content: center;
}

.p-virtualscroller-loading-icon {
    font-size: ${n("virtualscroller.loader.icon.size")};
    width: ${n("virtualscroller.loader.icon.size")};
    height: ${n("virtualscroller.loader.icon.size")};
}

.p-virtualscroller-horizontal > .p-virtualscroller-content {
    display: flex;
}

.p-virtualscroller-inline .p-virtualscroller-content {
    position: static;
}
`,Ut=(()=>{class n extends N{name="virtualscroller";theme=Oe;static \u0275fac=(()=>{let t;return function(i){return(t||(t=b(n)))(i||n)}})();static \u0275prov=D({token:n,factory:n.\u0275fac})}return n})();var Pe=(()=>{class n extends O{zone;get id(){return this._id}set id(t){this._id=t}get style(){return this._style}set style(t){this._style=t}get styleClass(){return this._styleClass}set styleClass(t){this._styleClass=t}get tabindex(){return this._tabindex}set tabindex(t){this._tabindex=t}get items(){return this._items}set items(t){this._items=t}get itemSize(){return this._itemSize}set itemSize(t){this._itemSize=t}get scrollHeight(){return this._scrollHeight}set scrollHeight(t){this._scrollHeight=t}get scrollWidth(){return this._scrollWidth}set scrollWidth(t){this._scrollWidth=t}get orientation(){return this._orientation}set orientation(t){this._orientation=t}get step(){return this._step}set step(t){this._step=t}get delay(){return this._delay}set delay(t){this._delay=t}get resizeDelay(){return this._resizeDelay}set resizeDelay(t){this._resizeDelay=t}get appendOnly(){return this._appendOnly}set appendOnly(t){this._appendOnly=t}get inline(){return this._inline}set inline(t){this._inline=t}get lazy(){return this._lazy}set lazy(t){this._lazy=t}get disabled(){return this._disabled}set disabled(t){this._disabled=t}get loaderDisabled(){return this._loaderDisabled}set loaderDisabled(t){this._loaderDisabled=t}get columns(){return this._columns}set columns(t){this._columns=t}get showSpacer(){return this._showSpacer}set showSpacer(t){this._showSpacer=t}get showLoader(){return this._showLoader}set showLoader(t){this._showLoader=t}get numToleratedItems(){return this._numToleratedItems}set numToleratedItems(t){this._numToleratedItems=t}get loading(){return this._loading}set loading(t){this._loading=t}get autoSize(){return this._autoSize}set autoSize(t){this._autoSize=t}get trackBy(){return this._trackBy}set trackBy(t){this._trackBy=t}get options(){return this._options}set options(t){this._options=t,t&&typeof t=="object"&&(Object.entries(t).forEach(([e,i])=>this[`_${e}`]!==i&&(this[`_${e}`]=i)),Object.entries(t).forEach(([e,i])=>this[`${e}`]!==i&&(this[`${e}`]=i)))}onLazyLoad=new dt;onScroll=new dt;onScrollIndexChange=new dt;elementViewChild;contentViewChild;height;_id;_style;_styleClass;_tabindex=0;_items;_itemSize=0;_scrollHeight;_scrollWidth;_orientation="vertical";_step=0;_delay=0;_resizeDelay=10;_appendOnly=!1;_inline=!1;_lazy=!1;_disabled=!1;_loaderDisabled=!1;_columns;_showSpacer=!0;_showLoader=!1;_numToleratedItems;_loading;_autoSize=!1;_trackBy;_options;d_loading=!1;d_numToleratedItems;contentEl;contentTemplate;itemTemplate;loaderTemplate;loaderIconTemplate;templates;_contentTemplate;_itemTemplate;_loaderTemplate;_loaderIconTemplate;first=0;last=0;page=0;isRangeChanged=!1;numItemsInViewport=0;lastScrollPos=0;lazyLoadState={};loaderArr=[];spacerStyle={};contentStyle={};scrollTimeout;resizeTimeout;initialized=!1;windowResizeListener;defaultWidth;defaultHeight;defaultContentWidth;defaultContentHeight;_contentStyleClass;get contentStyleClass(){return this._contentStyleClass}set contentStyleClass(t){this._contentStyleClass=t}get vertical(){return this._orientation==="vertical"}get horizontal(){return this._orientation==="horizontal"}get both(){return this._orientation==="both"}get loadedItems(){return this._items&&!this.d_loading?this.both?this._items.slice(this._appendOnly?0:this.first.rows,this.last.rows).map(t=>this._columns?t:t.slice(this._appendOnly?0:this.first.cols,this.last.cols)):this.horizontal&&this._columns?this._items:this._items.slice(this._appendOnly?0:this.first,this.last):[]}get loadedRows(){return this.d_loading?this._loaderDisabled?this.loaderArr:[]:this.loadedItems}get loadedColumns(){return this._columns&&(this.both||this.horizontal)?this.d_loading&&this._loaderDisabled?this.both?this.loaderArr[0]:this.loaderArr:this._columns.slice(this.both?this.first.cols:this.first,this.both?this.last.cols:this.last):this._columns}_componentStyle=T(Ut);constructor(t){super(),this.zone=t}ngOnInit(){super.ngOnInit(),this.setInitialState()}ngOnChanges(t){super.ngOnChanges(t);let e=!1;if(this.scrollHeight=="100%"&&(this.height="100%"),t.loading){let{previousValue:i,currentValue:o}=t.loading;this.lazy&&i!==o&&o!==this.d_loading&&(this.d_loading=o,e=!0)}if(t.orientation&&(this.lastScrollPos=this.both?{top:0,left:0}:0),t.numToleratedItems){let{previousValue:i,currentValue:o}=t.numToleratedItems;i!==o&&o!==this.d_numToleratedItems&&(this.d_numToleratedItems=o)}if(t.options){let{previousValue:i,currentValue:o}=t.options;this.lazy&&i?.loading!==o?.loading&&o?.loading!==this.d_loading&&(this.d_loading=o.loading,e=!0),i?.numToleratedItems!==o?.numToleratedItems&&o?.numToleratedItems!==this.d_numToleratedItems&&(this.d_numToleratedItems=o.numToleratedItems)}this.initialized&&!e&&(t.items?.previousValue?.length!==t.items?.currentValue?.length||t.itemSize||t.scrollHeight||t.scrollWidth)&&(this.init(),this.calculateAutoSize())}ngAfterContentInit(){this.templates.forEach(t=>{switch(t.getType()){case"content":this._contentTemplate=t.template;break;case"item":this._itemTemplate=t.template;break;case"loader":this._loaderTemplate=t.template;break;case"loadericon":this._loaderIconTemplate=t.template;break;default:this._itemTemplate=t.template;break}})}ngAfterViewInit(){super.ngAfterViewInit(),Promise.resolve().then(()=>{this.viewInit()})}ngAfterViewChecked(){this.initialized||this.viewInit()}ngOnDestroy(){this.unbindResizeListener(),this.contentEl=null,this.initialized=!1,super.ngOnDestroy()}viewInit(){rt(this.platformId)&&!this.initialized&&_t(this.elementViewChild?.nativeElement)&&(this.setInitialState(),this.setContentEl(this.contentEl),this.init(),this.defaultWidth=q(this.elementViewChild?.nativeElement),this.defaultHeight=Q(this.elementViewChild?.nativeElement),this.defaultContentWidth=q(this.contentEl),this.defaultContentHeight=Q(this.contentEl),this.initialized=!0)}init(){this._disabled||(this.setSize(),this.calculateOptions(),this.setSpacerSize(),this.bindResizeListener(),this.cd.detectChanges())}setContentEl(t){this.contentEl=t||this.contentViewChild?.nativeElement||Wt(this.elementViewChild?.nativeElement,".p-virtualscroller-content")}setInitialState(){this.first=this.both?{rows:0,cols:0}:0,this.last=this.both?{rows:0,cols:0}:0,this.numItemsInViewport=this.both?{rows:0,cols:0}:0,this.lastScrollPos=this.both?{top:0,left:0}:0,this.d_loading=this._loading||!1,this.d_numToleratedItems=this._numToleratedItems,this.loaderArr=[]}getElementRef(){return this.elementViewChild}getPageByFirst(t){return Math.floor(((t??this.first)+this.d_numToleratedItems*4)/(this._step||1))}isPageChanged(t){return this._step?this.page!==this.getPageByFirst(t??this.first):!0}scrollTo(t){this.elementViewChild?.nativeElement?.scrollTo(t)}scrollToIndex(t,e="auto"){if(this.both?t.every(o=>o>-1):t>-1){let o=this.first,{scrollTop:s=0,scrollLeft:l=0}=this.elementViewChild?.nativeElement,{numToleratedItems:r}=this.calculateNumItems(),d=this.getContentPosition(),c=this.itemSize,p=(u=0,m)=>u<=m?0:u,g=(u,m,V)=>u*m+V,f=(u=0,m=0)=>this.scrollTo({left:u,top:m,behavior:e}),y=this.both?{rows:0,cols:0}:0,C=!1,h=!1;this.both?(y={rows:p(t[0],r[0]),cols:p(t[1],r[1])},f(g(y.cols,c[1],d.left),g(y.rows,c[0],d.top)),h=this.lastScrollPos.top!==s||this.lastScrollPos.left!==l,C=y.rows!==o.rows||y.cols!==o.cols):(y=p(t,r),this.horizontal?f(g(y,c,d.left),s):f(l,g(y,c,d.top)),h=this.lastScrollPos!==(this.horizontal?l:s),C=y!==o),this.isRangeChanged=C,h&&(this.first=y)}}scrollInView(t,e,i="auto"){if(e){let{first:o,viewport:s}=this.getRenderedRange(),l=(c=0,p=0)=>this.scrollTo({left:c,top:p,behavior:i}),r=e==="to-start",d=e==="to-end";if(r){if(this.both)s.first.rows-o.rows>t[0]?l(s.first.cols*this._itemSize[1],(s.first.rows-1)*this._itemSize[0]):s.first.cols-o.cols>t[1]&&l((s.first.cols-1)*this._itemSize[1],s.first.rows*this._itemSize[0]);else if(s.first-o>t){let c=(s.first-1)*this._itemSize;this.horizontal?l(c,0):l(0,c)}}else if(d){if(this.both)s.last.rows-o.rows<=t[0]+1?l(s.first.cols*this._itemSize[1],(s.first.rows+1)*this._itemSize[0]):s.last.cols-o.cols<=t[1]+1&&l((s.first.cols+1)*this._itemSize[1],s.first.rows*this._itemSize[0]);else if(s.last-o<=t+1){let c=(s.first+1)*this._itemSize;this.horizontal?l(c,0):l(0,c)}}}else this.scrollToIndex(t,i)}getRenderedRange(){let t=(o,s)=>s||o?Math.floor(o/(s||o)):0,e=this.first,i=0;if(this.elementViewChild?.nativeElement){let{scrollTop:o,scrollLeft:s}=this.elementViewChild.nativeElement;if(this.both)e={rows:t(o,this._itemSize[0]),cols:t(s,this._itemSize[1])},i={rows:e.rows+this.numItemsInViewport.rows,cols:e.cols+this.numItemsInViewport.cols};else{let l=this.horizontal?s:o;e=t(l,this._itemSize),i=e+this.numItemsInViewport}}return{first:this.first,last:this.last,viewport:{first:e,last:i}}}calculateNumItems(){let t=this.getContentPosition(),e=(this.elementViewChild?.nativeElement?this.elementViewChild.nativeElement.offsetWidth-t.left:0)||0,i=(this.elementViewChild?.nativeElement?this.elementViewChild.nativeElement.offsetHeight-t.top:0)||0,o=(d,c)=>c||d?Math.ceil(d/(c||d)):0,s=d=>Math.ceil(d/2),l=this.both?{rows:o(i,this._itemSize[0]),cols:o(e,this._itemSize[1])}:o(this.horizontal?e:i,this._itemSize),r=this.d_numToleratedItems||(this.both?[s(l.rows),s(l.cols)]:s(l));return{numItemsInViewport:l,numToleratedItems:r}}calculateOptions(){let{numItemsInViewport:t,numToleratedItems:e}=this.calculateNumItems(),i=(l,r,d,c=!1)=>this.getLast(l+r+(l<d?2:3)*d,c),o=this.first,s=this.both?{rows:i(this.first.rows,t.rows,e[0]),cols:i(this.first.cols,t.cols,e[1],!0)}:i(this.first,t,e);this.last=s,this.numItemsInViewport=t,this.d_numToleratedItems=e,this.showLoader&&(this.loaderArr=this.both?Array.from({length:t.rows}).map(()=>Array.from({length:t.cols})):Array.from({length:t})),this._lazy&&Promise.resolve().then(()=>{this.lazyLoadState={first:this._step?this.both?{rows:0,cols:o.cols}:0:o,last:Math.min(this._step?this._step:this.last,this.items.length)},this.handleEvents("onLazyLoad",this.lazyLoadState)})}calculateAutoSize(){this._autoSize&&!this.d_loading&&Promise.resolve().then(()=>{if(this.contentEl){this.contentEl.style.minHeight=this.contentEl.style.minWidth="auto",this.contentEl.style.position="relative",this.elementViewChild.nativeElement.style.contain="none";let[t,e]=[q(this.contentEl),Q(this.contentEl)];t!==this.defaultContentWidth&&(this.elementViewChild.nativeElement.style.width=""),e!==this.defaultContentHeight&&(this.elementViewChild.nativeElement.style.height="");let[i,o]=[q(this.elementViewChild.nativeElement),Q(this.elementViewChild.nativeElement)];(this.both||this.horizontal)&&(this.elementViewChild.nativeElement.style.width=i<this.defaultWidth?i+"px":this._scrollWidth||this.defaultWidth+"px"),(this.both||this.vertical)&&(this.elementViewChild.nativeElement.style.height=o<this.defaultHeight?o+"px":this._scrollHeight||this.defaultHeight+"px"),this.contentEl.style.minHeight=this.contentEl.style.minWidth="",this.contentEl.style.position="",this.elementViewChild.nativeElement.style.contain=""}})}getLast(t=0,e=!1){return this._items?Math.min(e?(this._columns||this._items[0]).length:this._items.length,t):0}getContentPosition(){if(this.contentEl){let t=getComputedStyle(this.contentEl),e=parseFloat(t.paddingLeft)+Math.max(parseFloat(t.left)||0,0),i=parseFloat(t.paddingRight)+Math.max(parseFloat(t.right)||0,0),o=parseFloat(t.paddingTop)+Math.max(parseFloat(t.top)||0,0),s=parseFloat(t.paddingBottom)+Math.max(parseFloat(t.bottom)||0,0);return{left:e,right:i,top:o,bottom:s,x:e+i,y:o+s}}return{left:0,right:0,top:0,bottom:0,x:0,y:0}}setSize(){if(this.elementViewChild?.nativeElement){let t=this.elementViewChild.nativeElement.parentElement.parentElement,e=this._scrollWidth||`${this.elementViewChild.nativeElement.offsetWidth||t.offsetWidth}px`,i=this._scrollHeight||`${this.elementViewChild.nativeElement.offsetHeight||t.offsetHeight}px`,o=(s,l)=>this.elementViewChild.nativeElement.style[s]=l;this.both||this.horizontal?(o("height",i),o("width",e)):o("height",i)}}setSpacerSize(){if(this._items){let t=this.getContentPosition(),e=(i,o,s,l=0)=>this.spacerStyle=gt(ct({},this.spacerStyle),{[`${i}`]:(o||[]).length*s+l+"px"});this.both?(e("height",this._items,this._itemSize[0],t.y),e("width",this._columns||this._items[1],this._itemSize[1],t.x)):this.horizontal?e("width",this._columns||this._items,this._itemSize,t.x):e("height",this._items,this._itemSize,t.y)}}setContentPosition(t){if(this.contentEl&&!this._appendOnly){let e=t?t.first:this.first,i=(s,l)=>s*l,o=(s=0,l=0)=>this.contentStyle=gt(ct({},this.contentStyle),{transform:`translate3d(${s}px, ${l}px, 0)`});if(this.both)o(i(e.cols,this._itemSize[1]),i(e.rows,this._itemSize[0]));else{let s=i(e,this._itemSize);this.horizontal?o(s,0):o(0,s)}}}onScrollPositionChange(t){let e=t.target,i=this.getContentPosition(),o=(h,u)=>h?h>u?h-u:h:0,s=(h,u)=>u||h?Math.floor(h/(u||h)):0,l=(h,u,m,V,P,j)=>h<=P?P:j?m-V-P:u+P-1,r=(h,u,m,V,P,j,at)=>h<=j?0:Math.max(0,at?h<u?m:h-j:h>u?m:h-2*j),d=(h,u,m,V,P,j=!1)=>{let at=u+V+2*P;return h>=P&&(at+=P+1),this.getLast(at,j)},c=o(e.scrollTop,i.top),p=o(e.scrollLeft,i.left),g=this.both?{rows:0,cols:0}:0,f=this.last,y=!1,C=this.lastScrollPos;if(this.both){let h=this.lastScrollPos.top<=c,u=this.lastScrollPos.left<=p;if(!this._appendOnly||this._appendOnly&&(h||u)){let m={rows:s(c,this._itemSize[0]),cols:s(p,this._itemSize[1])},V={rows:l(m.rows,this.first.rows,this.last.rows,this.numItemsInViewport.rows,this.d_numToleratedItems[0],h),cols:l(m.cols,this.first.cols,this.last.cols,this.numItemsInViewport.cols,this.d_numToleratedItems[1],u)};g={rows:r(m.rows,V.rows,this.first.rows,this.last.rows,this.numItemsInViewport.rows,this.d_numToleratedItems[0],h),cols:r(m.cols,V.cols,this.first.cols,this.last.cols,this.numItemsInViewport.cols,this.d_numToleratedItems[1],u)},f={rows:d(m.rows,g.rows,this.last.rows,this.numItemsInViewport.rows,this.d_numToleratedItems[0]),cols:d(m.cols,g.cols,this.last.cols,this.numItemsInViewport.cols,this.d_numToleratedItems[1],!0)},y=g.rows!==this.first.rows||f.rows!==this.last.rows||g.cols!==this.first.cols||f.cols!==this.last.cols||this.isRangeChanged,C={top:c,left:p}}}else{let h=this.horizontal?p:c,u=this.lastScrollPos<=h;if(!this._appendOnly||this._appendOnly&&u){let m=s(h,this._itemSize),V=l(m,this.first,this.last,this.numItemsInViewport,this.d_numToleratedItems,u);g=r(m,V,this.first,this.last,this.numItemsInViewport,this.d_numToleratedItems,u),f=d(m,g,this.last,this.numItemsInViewport,this.d_numToleratedItems),y=g!==this.first||f!==this.last||this.isRangeChanged,C=h}}return{first:g,last:f,isRangeChanged:y,scrollPos:C}}onScrollChange(t){let{first:e,last:i,isRangeChanged:o,scrollPos:s}=this.onScrollPositionChange(t);if(o){let l={first:e,last:i};if(this.setContentPosition(l),this.first=e,this.last=i,this.lastScrollPos=s,this.handleEvents("onScrollIndexChange",l),this._lazy&&this.isPageChanged(e)){let r={first:this._step?Math.min(this.getPageByFirst(e)*this._step,this.items.length-this._step):e,last:Math.min(this._step?(this.getPageByFirst(e)+1)*this._step:i,this.items.length)};(this.lazyLoadState.first!==r.first||this.lazyLoadState.last!==r.last)&&this.handleEvents("onLazyLoad",r),this.lazyLoadState=r}}}onContainerScroll(t){if(this.handleEvents("onScroll",{originalEvent:t}),this._delay&&this.isPageChanged()){if(this.scrollTimeout&&clearTimeout(this.scrollTimeout),!this.d_loading&&this.showLoader){let{isRangeChanged:e}=this.onScrollPositionChange(t);(e||(this._step?this.isPageChanged():!1))&&(this.d_loading=!0,this.cd.detectChanges())}this.scrollTimeout=setTimeout(()=>{this.onScrollChange(t),this.d_loading&&this.showLoader&&(!this._lazy||this._loading===void 0)&&(this.d_loading=!1,this.page=this.getPageByFirst()),this.cd.detectChanges()},this._delay)}else!this.d_loading&&this.onScrollChange(t)}bindResizeListener(){rt(this.platformId)&&(this.windowResizeListener||this.zone.runOutsideAngular(()=>{let t=this.document.defaultView,e=Bt()?"orientationchange":"resize";this.windowResizeListener=this.renderer.listen(t,e,this.onWindowResize.bind(this))}))}unbindResizeListener(){this.windowResizeListener&&(this.windowResizeListener(),this.windowResizeListener=null)}onWindowResize(){this.resizeTimeout&&clearTimeout(this.resizeTimeout),this.resizeTimeout=setTimeout(()=>{if(_t(this.elementViewChild?.nativeElement)){let[t,e]=[q(this.elementViewChild?.nativeElement),Q(this.elementViewChild?.nativeElement)],[i,o]=[t!==this.defaultWidth,e!==this.defaultHeight];(this.both?i||o:this.horizontal?i:this.vertical?o:!1)&&this.zone.run(()=>{this.d_numToleratedItems=this._numToleratedItems,this.defaultWidth=t,this.defaultHeight=e,this.defaultContentWidth=q(this.contentEl),this.defaultContentHeight=Q(this.contentEl),this.init()})}},this._resizeDelay)}handleEvents(t,e){return this.options&&this.options[t]?this.options[t](e):this[t].emit(e)}getContentOptions(){return{contentStyleClass:`p-virtualscroller-content ${this.d_loading?"p-virtualscroller-loading":""}`,items:this.loadedItems,getItemOptions:t=>this.getOptions(t),loading:this.d_loading,getLoaderOptions:(t,e)=>this.getLoaderOptions(t,e),itemSize:this._itemSize,rows:this.loadedRows,columns:this.loadedColumns,spacerStyle:this.spacerStyle,contentStyle:this.contentStyle,vertical:this.vertical,horizontal:this.horizontal,both:this.both}}getOptions(t){let e=(this._items||[]).length,i=this.both?this.first.rows+t:this.first+t;return{index:i,count:e,first:i===0,last:i===e-1,even:i%2===0,odd:i%2!==0}}getLoaderOptions(t,e){let i=this.loaderArr.length;return ct({index:t,count:i,first:t===0,last:t===i-1,even:t%2===0,odd:t%2!==0},e)}static \u0275fac=function(e){return new(e||n)(ht(It))};static \u0275cmp=z({type:n,selectors:[["p-scroller"],["p-virtualscroller"],["p-virtual-scroller"],["p-virtualScroller"]],contentQueries:function(e,i,o){if(e&1&&(K(o,Gt,4),K(o,oe,4),K(o,se,4),K(o,le,4),K(o,At,4)),e&2){let s;R(s=A())&&(i.contentTemplate=s.first),R(s=A())&&(i.itemTemplate=s.first),R(s=A())&&(i.loaderTemplate=s.first),R(s=A())&&(i.loaderIconTemplate=s.first),R(s=A())&&(i.templates=s)}},viewQuery:function(e,i){if(e&1&&(yt(re,5),yt(Gt,5)),e&2){let o;R(o=A())&&(i.elementViewChild=o.first),R(o=A())&&(i.contentViewChild=o.first)}},hostVars:2,hostBindings:function(e,i){e&2&&Tt("height",i.height)},inputs:{id:"id",style:"style",styleClass:"styleClass",tabindex:"tabindex",items:"items",itemSize:"itemSize",scrollHeight:"scrollHeight",scrollWidth:"scrollWidth",orientation:"orientation",step:"step",delay:"delay",resizeDelay:"resizeDelay",appendOnly:"appendOnly",inline:"inline",lazy:"lazy",disabled:"disabled",loaderDisabled:"loaderDisabled",columns:"columns",showSpacer:"showSpacer",showLoader:"showLoader",numToleratedItems:"numToleratedItems",loading:"loading",autoSize:"autoSize",trackBy:"trackBy",options:"options"},outputs:{onLazyLoad:"onLazyLoad",onScroll:"onScroll",onScrollIndexChange:"onScrollIndexChange"},features:[k([Ut]),v,Ct],ngContentSelectors:ae,decls:3,vars:2,consts:[["disabledContainer",""],["element",""],["buildInContent",""],["content",""],["buildInLoader",""],["buildInLoaderIcon",""],[4,"ngIf","ngIfElse"],[3,"scroll","ngStyle","ngClass"],["class","p-virtualscroller-spacer",3,"ngStyle",4,"ngIf"],["class","p-virtualscroller-loader",3,"ngClass",4,"ngIf"],[4,"ngTemplateOutlet","ngTemplateOutletContext"],[3,"ngClass"],[4,"ngFor","ngForOf","ngForTrackBy"],[1,"p-virtualscroller-spacer",3,"ngStyle"],[1,"p-virtualscroller-loader",3,"ngClass"],[4,"ngFor","ngForOf"],[3,"styleClass"],[4,"ngIf"]],template:function(e,i){if(e&1&&(Y(),x(0,Fe,8,16,"ng-container",6)(1,Me,2,1,"ng-template",null,0,lt)),e&2){let o=ot(2);_("ngIf",!i._disabled)("ngIfElse",o)}},dependencies:[et,Ot,Pt,Dt,Ht,$t,jt,it],encapsulation:2})}return n})(),cn=(()=>{class n{static \u0275fac=function(e){return new(e||n)};static \u0275mod=H({type:n});static \u0275inj=$({imports:[Pe,it,it]})}return n})();export{Xe as a,ti as b,jt as c,wt as d,Qt as e,pi as f,fi as g,qe as h,Ze as i,Ti as j,Ai as k,Pe as l,cn as m};
