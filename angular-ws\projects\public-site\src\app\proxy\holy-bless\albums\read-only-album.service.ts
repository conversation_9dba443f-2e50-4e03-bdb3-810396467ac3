import type { AlbumAggregateDto, AlbumDto, AlbumSearchDto } from './dtos/models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class ReadOnlyAlbumService {
  apiName = 'Default';
  

  getAlbumFiles = (albumId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, AlbumAggregateDto>({
      method: 'GET',
      url: `/api/app/read-only-album/album-files/${albumId}`,
    },
    { apiName: this.apiName,...config });
  

  getAlbumFilesByContentCode = (contentCode: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, AlbumAggregateDto>({
      method: 'GET',
      url: '/api/app/read-only-album/album-files-by-content-code',
      params: { contentCode },
    },
    { apiName: this.apiName,...config });
  

  getList = (input: AlbumSearchDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<AlbumDto>>({
      method: 'GET',
      url: '/api/app/read-only-album',
      params: { channelId: input.channelId, albumType: input.albumType, channelContentCode: input.channelContentCode, sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
