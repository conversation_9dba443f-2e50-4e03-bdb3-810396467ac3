import { Component, ElementRef, inject, signal, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TreeNode } from 'primeng/api';
import { TreeModule } from 'primeng/tree';
import { ReadOnlyCollectionService } from '@/proxy/holy-bless/collections';
import { ActivatedRoute } from '@angular/router';
import { ReadOnlyArticleService } from '@/proxy/holy-bless/articles';
import { ArticleAggregateResult } from '@/proxy/holy-bless/results';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { MediaType } from '@/proxy/holy-bless/enums';
import { I18nService } from '@/services/i18n.service';
import { Subscription } from 'rxjs';
import { RemoveExtensionPipe } from '@/pipes/remove-extension.pipe';
import { LoadingService } from '@/services/loading.service';

interface TocItem {
  href: string;
  text: string;
  anchorId: string;
}

@Component({
  selector: 'app-collection-artical-tree',
  standalone: true,
  imports: [CommonModule, TreeModule, RemoveExtensionPipe],
  templateUrl: './collection-article-tree.html',
  styleUrls: ['./collection-article-tree.scss'],
})
export class CollectionArticleTreeComponent {
  #ReadOnlyCollectionService = inject(ReadOnlyCollectionService);
  #ReadOnlyArticleService = inject(ReadOnlyArticleService);
  #route = inject(ActivatedRoute);
  sanitizer = inject(DomSanitizer);
  elementRef = inject(ElementRef);
  i18nService = inject(I18nService);
  subs = new Subscription();
  loadingService = inject(LoadingService);

  contentCode: string | null = null;
  files: TreeNode[] = [];
  articleDetail = signal<ArticleAggregateResult | null>(null);
  articleFiles = computed(() => this.articleDetail()?.articleFiles || []);
  notImageArticleFiles = computed(
    () =>
      this.articleDetail()?.articleFiles.filter(
        (file) => file.mediaType !== MediaType.Image,
      ) || [],
  );
  selectedFile!: TreeNode;

  tocItems = signal<TocItem[]>([]);
  articleContent = signal<SafeHtml>('');

  ngOnInit() {
    this.#route.queryParams.subscribe((params) => {
      if (!params['contentCode']) return;
      this.contentCode = params['contentCode'];
      this.subs.unsubscribe();
      this.subs = new Subscription();
      const sub = this.i18nService.language$.subscribe(() => {
        this.loadCollectionSummary();
      });
      this.subs.add(sub);
    });
  }

  loadCollectionSummary() {
    this.loadingService.show();
    this.#ReadOnlyCollectionService
      .getCollectionTreeAndArticleTitlesByContentCode(this.contentCode!)
      .subscribe({
        next: (data) => {
          this.files = data.map((node: any) => this.buildTreeNode(node));
          this.loadingService.hide();
        },
        error: (error) => {
          console.error('获取文章树数据失败:', error);
          this.loadingService.hide();
        },
      });
  }

  buildTreeNode(node: any): TreeNode {
    return {
      key: node.id,
      label: node.name || node.title,
      data: node,
      children: node.articles
        ? node.articles.map((article: any) => this.buildTreeNode(article))
        : [],
    };
  }

  loadArticleDetail(node: TreeNode) {
    if (!node.key) return;
    this.loadingService.show();
    this.#ReadOnlyArticleService.getArticleAggregate(+node.key).subscribe({
      next: (data) => {
        this.articleDetail.set(data);

        this.extractTocFromContent();
        this.loadingService.hide();
      },
      error: (error) => {
        console.error('获取文章详情失败:', error);
        this.loadingService.hide();
      },
    });
  }

  private extractTocFromContent() {
    const content = this.articleDetail()?.content;
    if (!content) {
      this.tocItems.set([]);
      this.articleContent.set('');
      return;
    }

    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = content;

    const anchorLinks = tempDiv.querySelectorAll('a[href^="#"]');
    const tocItems: TocItem[] = [];

    anchorLinks.forEach((link) => {
      const href = link.getAttribute('href');
      const text = link.textContent?.trim();

      if (href && text) {
        const anchorId = href.substring(1); // 移除#号

        tocItems.push({
          href: href,
          text: text,
          anchorId: anchorId,
        });
      }
    });

    const targetLink = tempDiv.querySelector('a[name^="_"]');
    if (targetLink?.parentElement) {
      const parentElement = targetLink.parentElement;
      const parent = parentElement.parentNode;

      if (parent) {
        // 移除目标元素之前的所有兄弟节点
        let previousSibling = parentElement.previousSibling;
        while (previousSibling) {
          const nodeToRemove = previousSibling;
          previousSibling = previousSibling.previousSibling;
          parent.removeChild(nodeToRemove);
        }
      }
    }

    const uniqueTocItems = tocItems.filter(
      (item, index, self) =>
        index === self.findIndex((t) => t.anchorId === item.anchorId),
    );
    const safeHtml = this.sanitizer.bypassSecurityTrustHtml(tempDiv.innerHTML);
    this.articleContent.set(safeHtml);
    this.tocItems.set(uniqueTocItems);
  }

  onTocItemClick(tocItem: TocItem) {
    this.scrollToAnchor(tocItem.anchorId.trim());
  }

  private scrollToAnchor(anchorId: string) {
    const container = this.elementRef.nativeElement.querySelector(
      '.articaldetail-container',
    );
    if (!container) return;

    let targetElement = container.querySelector(
      `a[name="${anchorId}"]`,
    ) as HTMLElement;

    if (targetElement) {
      targetElement.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
        inline: 'nearest',
      });
    } else {
      console.warn(`未找到锚点: ${anchorId}`);
    }
  }

  trackByTocItem(index: number, item: TocItem): string {
    return item.anchorId;
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
