<p-tabs [(value)]="activeTab" (valueChange)="dataList = []">
  <p-tablist>
    <p-tab [value]="0">Article</p-tab>
    <p-tab [value]="1">File</p-tab>
  </p-tablist>
  <p-tabpanels>
    <p-tabpanel [value]="0">
      <div class="w-[20rem]">
        <div>
          <p class="mb-2">Keyword</p>
          <input type="text" pInputText [(ngModel)]="keyword" class="w-full" />
        </div>
        <div class="mt-4">
          <p class="mb-2">搜索范围</p>
          <div class="flex items-center gap-4">
            <div class="flex items-center">
              <p-checkbox
                class="flex items-center"
                inputId="ingredient1"
                name="searchFields"
                [value]="0"
                [(ngModel)]="searchFields"
              />
              <label for="ingredient1" class="ml-2"> 标题 </label>
            </div>
            <div class="flex items-center">
              <p-checkbox
                class="flex items-center"
                inputId="ingredient2"
                name="searchFields"
                [value]="1"
                [(ngModel)]="searchFields"
              />
              <label for="ingredient2" class="ml-2"> 正文 </label>
            </div>
          </div>
        </div>
        <div class="mt-4">
          <p class="mb-2">ArticleType</p>
          <p-multiselect
            [options]="articleContentCategoryOptions()"
            optionLabel="name"
            placeholder="All"
            class="w-full"
            [(ngModel)]="articleContentCategories"
          />
        </div>
        <div class="mt-4">
          <p class="mb-2">DeliverDate</p>
          <div class="flex items-center gap-4">
            <p-datepicker
              [iconDisplay]="'input'"
              [showIcon]="true"
              [(ngModel)]="deliveryDateStart"
            >
              <ng-template #inputicon let-clickCallBack="clickCallBack">
                <i class="pi pi-clock" (click)="clickCallBack($event)"></i>
              </ng-template>
            </p-datepicker>
            <p-datepicker [iconDisplay]="'input'" [showIcon]="true">
              <ng-template #inputicon let-clickCallBack="clickCallBack">
                <i class="pi pi-clock" (click)="clickCallBack($event)"></i>
              </ng-template>
            </p-datepicker>
          </div>
        </div>
        <div class="mt-4">
          <p-button label="Search" (onClick)="searchArticles()"></p-button>
        </div>
      </div>
    </p-tabpanel>
    <p-tabpanel [value]="1">
      <div class="w-[20rem]">
        <div>
          <p class="mb-2">FileName</p>
          <input type="text" pInputText [(ngModel)]="keyword" class="w-full" />
        </div>
        <div class="mt-4">
          <p class="mb-2">ContentType</p>
          <p-multiselect
            [options]="contentCategoryOptions()"
            [(ngModel)]="fileContentCategories"
            optionLabel="name"
            placeholder="Select a City"
            class="w-full"
          />
        </div>
        <div class="mt-4">
          <p class="mb-2">DeliverDate</p>
          <div class="flex items-center gap-4">
            <p-datepicker
              [iconDisplay]="'input'"
              [showIcon]="true"
              [(ngModel)]="deliveryDateStart"
            >
              <ng-template #inputicon let-clickCallBack="clickCallBack">
                <i class="pi pi-clock" (click)="clickCallBack($event)"></i>
              </ng-template>
            </p-datepicker>
            <p-datepicker
              [iconDisplay]="'input'"
              [showIcon]="true"
              [(ngModel)]="deliveryDateEnd"
            >
              <ng-template #inputicon let-clickCallBack="clickCallBack">
                <i class="pi pi-clock" (click)="clickCallBack($event)"></i>
              </ng-template>
            </p-datepicker>
          </div>
        </div>
        <div class="mt-4">
          <p-button label="Search" (onClick)="searchFiles()"></p-button>
        </div>
      </div>
    </p-tabpanel>
  </p-tabpanels>
</p-tabs>
<div>
  @if(dataList && dataList.length > 0) {
  <p-table
    [value]="dataList"
    [paginator]="true"
    [rows]="rows()"
    [first]="first()"
    [totalRecords]="totalRecords()"
    (onPage)="onPageChange($event)"
    [lazy]="true"
  >
    @if ( activeTab !== 0 ) {
    <ng-template #caption>
      <div class="flex items-center gap-2">
        <p-button icon="pi pi-cloud-download" label="下载" [outlined]="true" />
        <p-button icon="pi pi-play-circle" label="播放" [outlined]="true" />
      </div>
    </ng-template>
    }
    <ng-template #header>
      <tr>
        <th style="width: 4rem"><p-tableHeaderCheckbox /></th>
        <th>Name</th>
        @if ( activeTab !== 0 ) {
        <th>ContentCategory</th>
        }
        <th>LastModifiedTime</th>
      </tr>
    </ng-template>
    <ng-template #body let-product>
      <tr>
        <td>
          <p-tableCheckbox [value]="product" />
        </td>
        <td>{{ (product.title || product.fileName) | removeExtension }}</td>
        @if ( activeTab !== 0 ) {
        <td>{{ contentCategoryMap()[product.contentCategory] }}</td>
        }
        <td>
          {{ product.lastModificationTime | date: "yyyy-MM-dd HH:mm:ss" }}
        </td>
      </tr>
    </ng-template>
  </p-table>
  }
</div>
