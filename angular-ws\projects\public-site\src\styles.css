/* You can add global styles to this file, and also import other style files */
@import "./tailwind.css";
@import "primeicons/primeicons.css";

.app-default {
  --bannerbg: #fff5eb;
  --bannertext: #656363;
  --contentbg: #ffffff;
  --contenttext: #656363;
  --subcontenttext: #6b7280;
  --buttonborder: #d1d5db;
  --buttontext: #6b7280;
}

.app-green {
  --bannerbg: #c9e0cb;
  --bannertext: #516d58;
  --contentbg: #dce9dd;
  --contenttext: #656363;
  --subcontenttext: #6b7280;
  --buttonborder: #d1d5db;
  --buttontext: #6b7280;
}
.app-dark {
  --bannerbg: #121212;
}

body {
  color: var(--contenttext);
}
.app-container {
  background: var(--contentbg);
}
.p-menubar {
  background: var(--bannerbg) !important;
  border: unset
}
.p-card {
  height: 100%;
}
.p-card {
  height: 100%;
}

/* 主题色滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--surface-100, #f1f5f9);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: var(--p-primary-600);
  border-radius: 3px;
  opacity: 0.6;
}

::-webkit-scrollbar-thumb:hover {
  opacity: 0.8;
}