using HolyBless.Books.Dtos;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace HolyBless.Books;

public interface IReadOnlyEBookAppService : IApplicationService
{
    Task<EBookDto> GetAsync(int id);

    Task<PagedResultDto<EBookDto>> GetListAsync(PagedAndSortedResultRequestDto input);

    Task<EBookDto?> GetMatchedEBookAsync(int bookId);

    Task<EBookDto?> GetMatchedEBookByDeliveryDateAsync(DateTime deliveryDate);

    Task<List<EBookDto>> GetEBooksByChannelIdAsync(int channelId);

    Task<List<ChapterTreeDto>> GetChapterTreeByEBookIdAsync(int eBookId);
}