using System.Collections.Generic;
using System.Threading.Tasks;
using HolyBless.Channels.Dtos;
using Volo.Abp.Application.Dtos;

namespace HolyBless.Channels
{
    public interface IReadOnlyChannelAppService
    {
        Task<ChannelDto> GetAsync(int id);

        Task<PagedResultDto<ChannelDto>> GetListAsync(ChannelSearchDto input);

        Task<List<ChannelDto>> GetAllListByLanguageAsync(string languageCode = "zh-Hans");

        Task<ChannelDto?> GetMatchedChannelAsync(int channelId);

        Task<ChannelDto?> GetMatchedChannelByContentCodeAsync(string contentCode);

        Task<ChannelDto?> GetMatchedChannelByBookIdAsync(int bookId);

        Task<ChannelDto?> GetMatchedChannelByAlbumIdAsync(int albumId);

        Task<ChannelDto?> GetMatchedChannelByVirtualFolderIdAsync(int virtualFolderId);

        Task<List<ChannelTreeDto>> GetChannelTreeAsync(string languageCode);
    }
}