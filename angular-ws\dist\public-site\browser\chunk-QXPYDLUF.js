import{a as j}from"./chunk-M63GFVBH.js";import{a as N}from"./chunk-EYER3BI4.js";import{a as V}from"./chunk-N5RHZG25.js";import{a as L}from"./chunk-MJMCW5UB.js";import{a as H}from"./chunk-Y2PVWY7X.js";import"./chunk-MMC3E6BY.js";import{a as $,b as z}from"./chunk-QPTTFFWZ.js";import{d as P}from"./chunk-4JDMXTQG.js";import"./chunk-XXVSVAEK.js";import"./chunk-P4BOZY2U.js";import{c as D,d as U}from"./chunk-FUCZYBK4.js";import"./chunk-OTT6DUE3.js";import"./chunk-22JWGO27.js";import{ba as F,ua as O}from"./chunk-SXMRENJM.js";import"./chunk-BMA7WWEI.js";import{D as I,G as R,M as T,k as w}from"./chunk-GDGXRFMB.js";import{Ab as l,Bb as p,Fb as B,Kb as u,L as b,Lb as c,Pa as _,Ra as k,Ta as s,Vb as S,Wb as M,Z as n,a as d,cb as v,fa as g,ga as h,ib as x,qb as a,ta as C,xb as E,yb as y,zb as r}from"./chunk-YUW2MUHJ.js";import"./chunk-EQDQRRRY.js";var A=(i,t)=>t.id;function q(i,t){if(i&1&&p(0,"img",6),i&2){let e=c().$implicit;a("src",e.thumbnailUrl,_)("alt",e.title+" \u5C01\u9762\u56FE\u7247")}}function G(i,t){if(i&1){let e=B();r(0,"div",7),u("click",function(){g(e);let f=c().$implicit,m=c();return h(m.openBook(f.id))}),r(1,"h3",8),S(2),l()()}if(i&2){let e=c().$implicit;s(2),M(e.title)}}function J(i,t){i&1&&(r(0,"div",9),p(1,"p-button",10)(2,"p-button",11),l()),i&2&&(s(),a("outlined",!0),s(),a("outlined",!0))}function K(i,t){if(i&1&&(r(0,"p-card",2),x(1,q,1,2,"ng-template",3)(2,G,3,1,"ng-template",4)(3,J,3,2,"ng-template",5),l()),i&2){let e=t.$implicit;a("id","card-"+e.id)}}var W=class i{constructor(){this.route=n(I);this.#e=n(j);this.router=n(R);this.totalRecords=0;this.rows=10;this.first=0;this._isMobile=!1;this.collectionId=null;this.cardItems=C([]);this.channelIdContentCodeService=n(N);this.subs=new d;this.i18nService=n(H);this.#t=n(V);this.loadingService=n(L);this.checkMobile()}#e;#t;ngOnInit(){this.route.params.subscribe({next:t=>{let e=t.channelId;e&&(this.loadEBooks(e),this.changeLanguage(e))}})}changeLanguage(t){this.subs.unsubscribe(),this.subs=new d;let e=this.i18nService.language$.pipe(b(1)).subscribe(()=>{this.#t.getMatchedChannel(t).subscribe({next:o=>{if(!o){this.router.navigateByUrl("/landing");return}o.id!==t&&this.router.navigateByUrl(`/ebooks/ebook-card/${o.id}`)}})});this.subs.add(e)}loadEBooks(t){t&&(this.loadingService.show(),this.#e.getEBooksByChannelId(t).subscribe({next:e=>{this.cardItems.set(e),this.totalRecords=e.length,this.loadingService.hide()},error:e=>{console.error("\u83B7\u53D6\u7535\u5B50\u4E66\u6570\u636E\u5931\u8D25:",e),this.loadingService.hide()}}))}get isMobile(){return this._isMobile}get rowsPerPageOptions(){return this.isMobile?void 0:[10,20,50]}checkMobile(){this._isMobile=window.innerWidth<=768}openBook(t){t&&this.router.navigateByUrl(`/ebooks/book-detail/${t}`)}onPageChange(t){this.first=t.first,this.rows=t.rows,console.log("\u9875\u9762\u53D8\u5316:",t)}onResize(t){this.checkMobile()}static{this.\u0275fac=function(e){return new(e||i)}}static{this.\u0275cmp=v({type:i,selectors:[["app-ebook-card"]],hostBindings:function(e,o){e&1&&u("resize",function(m){return o.onResize(m)},!1,k)},decls:4,vars:0,consts:[[1,"flex","flex-col","p-6"],[1,"grid","grid-cols-1","md:grid-cols-4","lg:grid-cols-5","gap-6","mb-8"],["styleClass","card-item",3,"id"],["pTemplate","header"],["pTemplate","content"],["pTemplate","footer"],[1,"rounded-t-xl","w-56",3,"src","alt"],[1,"flex","cursor-pointer",3,"click"],[1,"text-lg","font-semibold","mb-2"],[1,"flex","justify-between","items-center"],["size","small","label","\u9605\u8BFB","icon","pi pi-book",3,"outlined"],["size","small","label","\u542C\u4E66","icon","pi pi-bookmark",3,"outlined"]],template:function(e,o){e&1&&(r(0,"div",0)(1,"div",1),E(2,K,4,1,"p-card",2,A),l()()),e&2&&(s(2),y(o.cardItems()))},dependencies:[w,T,z,$,F,O,P,U,D],styles:["[_nghost-%COMP%]     p-card{display:flex}"]})}};export{W as EbookCardComponent};
