import type { ChapterTreeDto, EBookDto } from './dtos/models';
import { RestService, Rest } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class ReadOnlyEBookService {
  apiName = 'Default';
  

  getChapterTreeByEBookId = (eBookId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ChapterTreeDto[]>({
      method: 'GET',
      url: `/api/app/read-only-eBook/chapter-tree-by-eBook-id/${eBookId}`,
    },
    { apiName: this.apiName,...config });
  

  getEBooksByChannelId = (channelId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, EBookDto[]>({
      method: 'GET',
      url: `/api/app/read-only-eBook/e-books-by-channel-id/${channelId}`,
    },
    { apiName: this.apiName,...config });
  

  getMatchedEBook = (bookId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, EBookDto>({
      method: 'GET',
      url: `/api/app/read-only-eBook/matched-eBook/${bookId}`,
    },
    { apiName: this.apiName,...config });
  

  getMatchedEBookByDeliveryDate = (deliveryDate: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, EBookDto>({
      method: 'GET',
      url: '/api/app/read-only-eBook/matched-eBook-by-delivery-date',
      params: { deliveryDate },
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
